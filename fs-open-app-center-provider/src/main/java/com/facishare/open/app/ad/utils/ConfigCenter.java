package com.facishare.open.app.ad.utils;

import com.github.autoconf.ConfigFactory;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Created by linchf on 2016/9/18.
 */
public class ConfigCenter {
    /**
     * 使用每日缓存的APP可见类型列表
     */
    public static List<String> DAY_CACHE_MODULE_LIST = new ArrayList<>();
    public ConfigCenter() {}

    static {
        ConfigFactory.getInstance().getConfig("fs-open-app-center-biz", config -> {
           DAY_CACHE_MODULE_LIST = Arrays.asList(config.get("DAY_CACHE_MODULE_LIST", "").split(","));
        });
    }
}
