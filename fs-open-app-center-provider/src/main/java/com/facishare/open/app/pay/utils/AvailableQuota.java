package com.facishare.open.app.pay.utils;

import com.facishare.open.app.pay.api.enums.PayStatus;
import com.facishare.open.app.pay.api.enums.QuotaType;
import com.facishare.open.app.pay.api.model.QuotaVo;
import com.facishare.open.app.pay.entity.QuotaRecord;
import org.springframework.util.CollectionUtils;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static com.facishare.open.app.pay.utils.QuotaUtils.MAX_TRIAL_QUOTA;

/**
 * Created by huanghl on 2016/7/25.
 *
 * <AUTHOR>
 */
public enum AvailableQuota {

    /**
     * 按照枚举的顺序依次判断。如果要新加，请注意顺序.
     */
    QUOTA_EXPIRED {
        //如果配额全部到期, 则显示最后到期的配额信息(试用到期/购买到期) <Copied from original>.
        @Override
        public QuotaVo getTheLatestQuota(List<QuotaRecord> quotaRecords, Date baseDate, Integer quota) {
            QuotaVo quotaVo = new QuotaVo();
            Optional<QuotaRecord> endQuotaOpt = quotaRecords.stream().max(Comparator.comparing(QuotaRecord::getGmtEnd));
            QuotaRecord quotaRecord = endQuotaOpt.get();
            quotaVo.setCanTryApp(false);//试用规则: 只要有配额记录, 不是已试用就是已购买, 都不允许试用 <Copied from original>.
            quotaVo.setAppId(quotaRecord.getAppId());
            quotaVo.setFsEa(quotaRecord.getFsEa());
            quotaVo.setPayStatus(quotaRecord.getType() == QuotaType.PURCHASE.getCode()
                    ? PayStatus.PURCHASE_EXPIRED : PayStatus.TRIAL_EXPIRED);
            quotaVo.setExpireDate(quotaRecord.getGmtEnd());
            quotaVo.setQuota(0);
            return quotaVo;
        }

        @Override
        protected boolean isMatching(List<QuotaRecord> quotaRecords, Date baseDate) {
            Optional<QuotaRecord> endQuotaOpt = quotaRecords.stream().max(Comparator.comparing(QuotaRecord::getGmtEnd));
            return endQuotaOpt.get().getGmtEnd().before(baseDate);
        }
    },
    QUOTA_AVAILABLE_NOW {
        //当前存在有效配额, 则返回有效期内类型以及下一个到期断点 <Copied from original>.
        //获取下一个到期断点 <Copied from original>.
        @Override
        public QuotaVo getTheLatestQuota(List<QuotaRecord> quotaRecords, Date baseDate, Integer quota) {
            QuotaVo quotaVo = new QuotaVo();
            Optional<QuotaRecord> curQuotaRecord = QuotaUtils.getFirstCurrent(baseDate, quotaRecords.stream());
            QuotaRecord quotaRecord = curQuotaRecord.get();
            quotaVo.setCanTryApp(false);//See QUOTA_EXPIRED
            quotaVo.setAppId(quotaRecord.getAppId());
            quotaVo.setFsEa(quotaRecord.getFsEa());
            quotaVo.setExpireDate(QuotaUtils.getNextBreakPoint(baseDate, quotaRecords.stream()).get());
            quotaVo.setPayStatus(quotaRecord.getType() == QuotaType.PURCHASE.getCode()
                    ? PayStatus.PURCHASED : PayStatus.ON_TRIAL);
            if (quota == null) {
                quotaVo.setQuota(0);
            } else if (quota > MAX_TRIAL_QUOTA) {
                if (quotaVo.getPayStatus() == PayStatus.ON_TRIAL) {
                    quotaVo.setQuota(MAX_TRIAL_QUOTA);
                } else {
                    quotaVo.setQuota(quota - MAX_TRIAL_QUOTA);
                }
            } else {
                quotaVo.setQuota(quota);
            }
            return quotaVo;
        }

        @Override
        protected boolean isMatching(List<QuotaRecord> quotaRecords, Date baseDate) {
            Optional<QuotaRecord> curQuotaRecord = QuotaUtils.getFirstCurrent(baseDate, quotaRecords.stream());
            return curQuotaRecord.isPresent();
        }
    },
    QUOTA_AVAILABLE_LATER {
        //如果无当前有效配额, 而有后续未启用配额 <Copied from original>.
        @Override
        public QuotaVo getTheLatestQuota(List<QuotaRecord> quotaRecords, Date baseDate, Integer quota) {
            Optional<QuotaRecord> nextUnapplied = QuotaUtils.getNextUnapplied(baseDate, quotaRecords.stream());
            QuotaVo quotaVo = new QuotaVo();
            QuotaRecord quotaRecord = nextUnapplied.get();
            quotaVo.setCanTryApp(false);//See QUOTA_EXPIRED
            quotaVo.setAppId(quotaRecord.getAppId());
            quotaVo.setFsEa(quotaRecord.getFsEa());
            quotaVo.setBeginDate(quotaRecord.getGmtBegin());
            quotaVo.setExpireDate(quotaRecord.getGmtEnd());
            quotaVo.setQuota(quotaRecord.getQuota());
            quotaVo.setPayStatus(PayStatus.PURCHASE_NOT_APPLIED);
            return quotaVo;
        }

        @Override
        protected boolean isMatching(List<QuotaRecord> quotaRecords, Date baseDate) {
            Optional<QuotaRecord> nextUnapplied = QuotaUtils.getNextUnapplied(baseDate, quotaRecords.stream());
            return nextUnapplied.isPresent();
        }
    },
    QUOTA_UNEXPECTED {
        //既无当前, 又无后续, 又无过去(这里必须是错误!!!) <Copied from original>.
        @Override
        public QuotaVo getTheLatestQuota(List<QuotaRecord> quotaRecords, Date baseDate, Integer quota) {
            QuotaRecord firstOne = quotaRecords.stream().findFirst().get();
            throw new RuntimeException(String.format("There is no last past quota record,fsEa[%s],appId[%s],now[%s]",
                    firstOne.getFsEa(), firstOne.getAppId(), baseDate));
        }

        @Override
        protected boolean isMatching(List<QuotaRecord> quotaRecords, Date baseDate) {
            Optional<QuotaRecord> lastPast = QuotaUtils.getLastPast(baseDate, quotaRecords.stream());
            return !lastPast.isPresent();
        }
    },
    QUOTA_DEFAULT {
        @Override
        public QuotaVo getTheLatestQuota(List<QuotaRecord> quotaRecords, Date baseDate, Integer quota) {
            Optional<QuotaRecord> lastPast = QuotaUtils.getLastPast(baseDate, quotaRecords.stream());
            QuotaRecord quotaRecord = lastPast.get();
            QuotaVo quotaVo = new QuotaVo();
            quotaVo.setCanTryApp(false);//See QUOTA_EXPIRED
            quotaVo.setAppId(quotaRecord.getAppId());
            quotaVo.setFsEa(quotaRecord.getFsEa());
            quotaVo.setQuota(0);
            quotaVo.setExpireDate(quotaRecord.getGmtEnd());
            quotaVo.setPayStatus(quotaRecord.getType() == QuotaType.PURCHASE.getCode()
                    ? PayStatus.PURCHASE_EXPIRED : PayStatus.TRIAL_EXPIRED);
            return quotaVo;
        }

        @Override
        protected boolean isMatching(List<QuotaRecord> quotaRecords, Date baseDate) {
            return true;
        }
    };

    private AvailableQuota() {
    }

    /**
     * Find the latest quota.
     * <p>
     * Note: This method is not well designed because @param quota is not common. Refactor if needed.
     *
     * @param quotaRecords quota records.
     * @param baseDate     the base date used for comparison.
     * @param quota        quota retrieved from database. Provide null if useless.
     * @return The latest quota VO.
     */
    public abstract QuotaVo getTheLatestQuota(List<QuotaRecord> quotaRecords, Date baseDate, Integer quota);

    /**
     * See if matching.
     *
     * @param quotaRecords quota records.
     * @param baseDate     the base date used for comparison.
     * @return true if expected, otherwise false.
     */
    protected abstract boolean isMatching(List<QuotaRecord> quotaRecords, Date baseDate);

    public static AvailableQuota valueOf(List<QuotaRecord> quotaRecords, Date baseDate) {
        if (CollectionUtils.isEmpty(quotaRecords) || baseDate == null) {
            throw new IllegalArgumentException("Both quotaRecords and baseDate are mandatory");
        }
        for (AvailableQuota quota : values()) {
            if (quota.isMatching(quotaRecords, baseDate)) {
                return quota;
            }
        }
        return QUOTA_DEFAULT;
    }
}
