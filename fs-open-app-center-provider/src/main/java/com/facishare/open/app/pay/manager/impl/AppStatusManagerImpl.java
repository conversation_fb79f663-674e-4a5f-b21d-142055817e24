package com.facishare.open.app.pay.manager.impl;

import com.facishare.ibss.data.fda.api.EnterpriseService;
import com.facishare.open.app.pay.cons.MqConstants;
import com.facishare.open.app.pay.entity.QuotaRecord;
import com.facishare.open.app.pay.manager.NotifyAppManager;
import com.facishare.open.app.pay.utils.CommonThreadPoolUtils;
import com.facishare.open.app.pay.utils.ConfigCenter;
import com.facishare.open.oauth.result.CreateOpenCorpIdResult;
import io.protostuff.ProtobufIOUtil;
import io.protostuff.Schema;
import io.protostuff.runtime.RuntimeSchema;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageListener;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * Created by xialf on 5/24/16.
 *
 * <AUTHOR>
 */
@Service
public class AppStatusManagerImpl implements MessageListener {
    private static Logger LOGGER = LoggerFactory.getLogger(AppStatusManagerImpl.class);

    @Resource
    private com.facishare.appserver.training.api.CorpService trainingCorpService;

    @Resource
    private com.facishare.open.oauth.service.CorpService oauthCorpService;

    @Resource(name = "payEnterpriseService")
    private EnterpriseService enterpriseService;

    @Resource
    private NotifyAppManager notifyAppManager;

    @Override
    public void onMessage(Message message) {
        final MessageProperties messageProperties = message.getMessageProperties();

        LOGGER.info("Received MQ type[{}], appId[{}]",
                messageProperties.getType(), messageProperties.getHeaders().get("appId"));
        switch (messageProperties.getType()) {
            case MqConstants.EA_PURCHASED:
            case MqConstants.EA_TRIED: {
                QuotaRecord quotaRecord = new QuotaRecord();
                Schema<QuotaRecord> schema = RuntimeSchema.getSchema(QuotaRecord.class);
                ProtobufIOUtil.mergeFrom(message.getBody(), quotaRecord, schema);
                appAdded(quotaRecord.getAppId(), quotaRecord.getFsEa());
                break;
            }
            case MqConstants.EA_EXPIRED: {
                QuotaRecord quotaRecord = new QuotaRecord();
                Schema<QuotaRecord> schema = RuntimeSchema.getSchema(QuotaRecord.class);
                ProtobufIOUtil.mergeFrom(message.getBody(), quotaRecord, schema);
                appExpired(quotaRecord.getAppId(), quotaRecord.getFsEa());
                break;
            }

//            case MqConstants.USER_TRIED: {
//                EmployeeTrial employeeTrial = new EmployeeTrial();
//                Schema<EmployeeTrial> schema = RuntimeSchema.getSchema(EmployeeTrial.class);
//                ProtobufIOUtil.mergeFrom(message.getBody(), employeeTrial, schema);
//                appAdded(employeeTrial.getAppId(), employeeTrial.getFsEa());
//                break;
//            }
//
//            case MqConstants.USER_EXPIRED: {
//                EmployeeTrial employeeTrial = new EmployeeTrial();
//                Schema<EmployeeTrial> schema = RuntimeSchema.getSchema(EmployeeTrial.class);
//                ProtobufIOUtil.mergeFrom(message.getBody(), employeeTrial, schema); //个人试用, 不需要提醒
//                notifyAppManager.notifyExpiration(employeeTrial.getFsEa(), employeeTrial.getAppId(), employeeTrial.getUserId());
//                break;
//            }

            default:
                LOGGER.warn("There is no handler for mq with type=[{}]", messageProperties);
                break;
        }
    }

    private void appAdded(String appId, String fsEa) {
        //todo 培训助手默认开启，此服务已下线
        //如果是培训助手, 则通知应用进行初始化
        if (appId.equals(ConfigCenter.getTrainingAssistAppId())) {
            CommonThreadPoolUtils.getExecutor().execute(() -> {
                CreateOpenCorpIdResult openCorpIdResult =
                        oauthCorpService.createOpenCorpId(fsEa, appId);
                if (!openCorpIdResult.isSuccess()) {
                    LOGGER.warn("fail to oauthCorpService.createOpenCorpId, fsEa[{}],appId[{}],result[{}]",
                            fsEa, appId, openCorpIdResult);
                } else {
                    try {
                        final boolean result = trainingCorpService.openCorpAuth(openCorpIdResult.getOpenCorpId());
                        LOGGER.info("trainingCorpService.openCorpAuth,fsEa[{}],appId[{}],corpId[{}],result[{}]",
                                fsEa, appId, openCorpIdResult.getOpenCorpId(), result);
                    } catch (Exception e) {
                        LOGGER.error("", e);
                    }
                }
            });
        }
    }

    private void appExpired(String appId, String fsEa) {
        CommonThreadPoolUtils.getExecutor().execute(() -> {
            try {
                notifyAppManager.notifyExpiration(fsEa, appId);
                enterpriseService.doAfterAppTimeout(fsEa, appId);
            } catch (Exception e) {
                LOGGER.warn("fail to notifyExpiration or notify Huiju, fsEa[{}],appId[{}]",
                        fsEa, appId, e);
            }

        });
    }
}
