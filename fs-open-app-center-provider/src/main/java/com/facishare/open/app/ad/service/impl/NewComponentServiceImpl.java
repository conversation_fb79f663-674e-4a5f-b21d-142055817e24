package com.facishare.open.app.ad.service.impl;

import com.facishare.open.app.ad.api.cons.AppAdConstant;
import com.facishare.open.app.ad.api.enums.AppAdCodeEnum;
import com.facishare.open.app.ad.api.service.NewComponentService;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.BaseResult;
import com.facishare.open.common.result.ErrCode;
import com.github.jedis.support.MergeJedisCmd;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by zenglb on 2016/1/12.
 */
@Service("newComponentServiceImpl")
public class NewComponentServiceImpl implements NewComponentService {
    private final Logger logger = LoggerFactory.getLogger(NewComponentServiceImpl.class);

    private static final String VAL = "YES";
    @Resource(name = "jedisSupport")
    private MergeJedisCmd jedis;

    @Override
    public BaseResult<List<Integer>> checkNewStatus(FsUserVO fsUser, List<String> componentIds) {
        if (null == fsUser || !FsUserVO.isFsUserString(fsUser.asStringUser()) || CollectionUtils.isEmpty(componentIds)) {
            return new BaseResult<>(AppAdCodeEnum.PARAM_ILLEGAL_EXCEPTION);
        }
        try {
            List<Integer> result = new ArrayList<>();
            for (String componentId : componentIds) {
                String key = getKey(fsUser.getEnterpriseAccount(), fsUser.getUserId(), componentId);
                String val = jedis.get(key);
                if (VAL.equals(val)) {
                    result.add(AppAdConstant.YES);
                } else {
                    result.add(AppAdConstant.NO);
                }
            }
            return new BaseResult<>(result);
        } catch (Exception e) {
            logger.error("checkNewStatus failed!", e);
            return new BaseResult<>(AppAdCodeEnum.SYSTEM_EXCEPTION);
        }
    }

    @Override
    public BaseResult<Void> setNewComponentUsers(FsUserVO fsUser, String componentId, List<Integer> userIds) {
        if (null == fsUser || !FsUserVO.isFsUserString(fsUser.asStringUser()) || StringUtils.isEmpty(componentId) || CollectionUtils.isEmpty(userIds)) {
            return new BaseResult<>(AppAdCodeEnum.PARAM_ILLEGAL_EXCEPTION);
        }
        try {
            for (Integer userId : userIds) {
                String key = getKey(fsUser.getEnterpriseAccount(), userId, componentId);
                jedis.set(key, VAL);
            }
            return new BaseResult<>();
        } catch (Exception e) {
            logger.error("setNewComponentUsers failed!", e);
            return new BaseResult<>(AppAdCodeEnum.SYSTEM_EXCEPTION);
        }
    }

    @Override
    public BaseResult<Void> cancelNewState(FsUserVO fsUser, String componentId) {
        BaseResult<Boolean> result = cancelNewStateAndGet(fsUser, componentId);
        return new BaseResult<>(new ErrCode() {
            @Override
            public int getErrCode() {
                return result.getErrCode();
            }

            @Override
            public String getErrMessage() {
                return result.getErrMessage();
            }

            @Override
            public String getErrDescription() {
                return result.getErrDescription();
            }
        });
    }

    private String getKey(String fsEa, Integer userId, String componentId) {
        return "open_app_ad_new_" + fsEa + "_" + userId + "_" + componentId;
    }

    @Override
    public BaseResult<Boolean> cancelNewStateAndGet(FsUserVO fsUser, String componentId) {
        if (null == fsUser || !FsUserVO.isFsUserString(fsUser.asStringUser()) || StringUtils.isEmpty(componentId)) {
            return new BaseResult<>(AppAdCodeEnum.PARAM_ILLEGAL_EXCEPTION);
        }

        try {
            String key = getKey(fsUser.getEnterpriseAccount(), fsUser.getUserId(), componentId);
            long result = jedis.del(key);
            return new BaseResult<>(result > 0);
        } catch (Exception e) {
            logger.error("clickComponent failed!", e);
            return new BaseResult<>(AppAdCodeEnum.SYSTEM_EXCEPTION);
        }
    }
}
