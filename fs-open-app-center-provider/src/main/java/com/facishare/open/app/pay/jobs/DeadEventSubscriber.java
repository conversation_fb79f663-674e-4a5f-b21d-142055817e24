package com.facishare.open.app.pay.jobs;

import com.google.common.eventbus.DeadEvent;
import com.google.common.eventbus.EventBus;
import com.google.common.eventbus.Subscribe;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * Created by xialf on 6/17/16.
 *
 * <AUTHOR>
 */
@Service
public class DeadEventSubscriber {
    private static final Logger LOGGER = LoggerFactory.getLogger(DeadEventSubscriber.class);

    @Resource
    private EventBus eventBus;

    @Subscribe
    public void handleDeadEvent(DeadEvent event) {
        LOGGER.warn("No subscriber for {}", event.getEvent());
    }

    @PostConstruct
    private void init() {
        eventBus.register(this);
    }
}
