package com.facishare.open.app.ad.manager.impl;

import com.facishare.open.app.ad.api.enums.AppAdBannerStatusEnum;
import com.facishare.open.app.ad.api.enums.AppAdBannerTypeEnum;
import com.facishare.open.app.ad.api.enums.ModuleKeyEnum;
import com.facishare.open.app.ad.api.model.FsAdAppBannerDO;
import com.facishare.open.app.ad.api.utils.IdGenerateUtils;
import com.facishare.open.app.ad.dao.FsAdAppBannerDAO;
import com.facishare.open.app.ad.manager.AppCenterBannerManager;
import com.facishare.open.app.ad.manager.CheckAppUpdatedManager;
import com.facishare.open.common.storage.mysql.dao.Pager;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date on 2016/01/08
 */
@Service
public class AppCenterBannerManagerImpl implements AppCenterBannerManager {

    @Resource
    private FsAdAppBannerDAO fsAdAppBannerDAO;
    @Resource
    private CheckAppUpdatedManager checkAppUpdatedManager;

    @Override
    public List<FsAdAppBannerDO> queryAppBanners(AppAdBannerTypeEnum appAdBannerTypeEnum) {
        List<Integer> statusList = new ArrayList<>();
        statusList.add(AppAdBannerStatusEnum.ENABLE.getCode());
        return fsAdAppBannerDAO.queryAppBannersByStatusAndType(statusList, appAdBannerTypeEnum.getCode());
    }

    @Override
    public Pager<FsAdAppBannerDO> queryAppBannerPager(Pager<FsAdAppBannerDO> pager) {
        Map<String, Object> params = pager.params();
        params.put("statusEnums", Arrays.asList(AppAdBannerStatusEnum.ENABLE.getCode(), AppAdBannerStatusEnum.DISABLE.getCode()));
        Long recordSize = fsAdAppBannerDAO.queryAppBannerPagerCount(params);
        pager.setRecordSize(recordSize.intValue());
        if (pager.getPageTotal() >= pager.getCurrentPage()) {
            List<FsAdAppBannerDO> adAppBannerDOs = fsAdAppBannerDAO.queryAppBannerPagerList(params);
            pager.setData(adAppBannerDOs);
        }
        return pager;
    }

    @Override
    public FsAdAppBannerDO queryBannerById(String bannerId) {
        return fsAdAppBannerDAO.queryBannerById(bannerId);
    }

    @Override
    public String addBanner(FsAdAppBannerDO fsAdAppBannerDO) {
        String id = IdGenerateUtils.generateUUID();
        fsAdAppBannerDO.setId(id);
        fsAdAppBannerDO.setStatus(AppAdBannerStatusEnum.DISABLE.getCode());
        fsAdAppBannerDAO.addBanner(fsAdAppBannerDO);
        checkAppUpdatedManager.resetTagByModuleKey(ModuleKeyEnum.BANNERS);
        return id ;
    }

    @Override
    public void updateBanner(FsAdAppBannerDO fsAdAppBannerDO) {
        fsAdAppBannerDAO.updateBanner(fsAdAppBannerDO);
        checkAppUpdatedManager.resetTagByModuleKey(ModuleKeyEnum.BANNERS);
    }

    @Override
    public void deleteBanner(String bannerId) {
        fsAdAppBannerDAO.updateBannerStatus(bannerId, AppAdBannerStatusEnum.DELETED.getCode());
        checkAppUpdatedManager.resetTagByModuleKey(ModuleKeyEnum.BANNERS);
    }

    @Override
    public void bannerEnable(String bannerId) {
        fsAdAppBannerDAO.updateBannerStatus(bannerId, AppAdBannerStatusEnum.ENABLE.getCode());
        checkAppUpdatedManager.resetTagByModuleKey(ModuleKeyEnum.BANNERS);
    }

    @Override
    public void bannerDisable(String bannerId) {
        fsAdAppBannerDAO.updateBannerStatus(bannerId, AppAdBannerStatusEnum.DISABLE.getCode());
        checkAppUpdatedManager.resetTagByModuleKey(ModuleKeyEnum.BANNERS);
    }

    @Override
    public void updateBannerIndex(String bannerId, Integer index) {
        fsAdAppBannerDAO.updateBannerIndex(bannerId, index);
        checkAppUpdatedManager.resetTagByModuleKey(ModuleKeyEnum.BANNERS);
    }

    @Override
    public FsAdAppBannerDO queryBannerByIndex(AppAdBannerTypeEnum appAdBannerTypeEnum, Integer index) {
        List<Integer> statuses = Arrays.asList(AppAdBannerStatusEnum.ENABLE.getCode(),AppAdBannerStatusEnum.DISABLE.getCode());
        return fsAdAppBannerDAO.queryBannerByIndex(appAdBannerTypeEnum.getCode(),index,statuses);
    }
}
