package com.facishare.open.app.pay.mq;

import com.facishare.open.app.pay.mq.api.item.CrmInstructItem;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageListener;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.stereotype.Service;

/**
 * Created by xialf on 7/28/16.
 *
 * <AUTHOR>
 */
@Service
public class SelfListener implements MessageListener {
    private static Logger LOGGER = LoggerFactory.getLogger(SelfListener.class);

    @Override
    public void onMessage(Message message) {
        final MessageProperties messageProperties = message.getMessageProperties();

        LOGGER.info("Received CRM View MQ type[{}], appId[{}]",
                messageProperties.getType(), messageProperties.getHeaders().get("appId"));
        final CrmInstructItem item = new CrmInstructItem();
        try {
            item.fromProto(message.getBody());
        } catch (Exception e) {
            LOGGER.error("fail to consumer message[{}]", message, e);
        }
        LOGGER.info("CrmInstructItem Message: {}", item);
    }
}
