package com.facishare.open.app.ad.mongo.dao.impl;

import com.facishare.open.app.ad.model.AppComponentSortDO;
import com.facishare.open.app.ad.mongo.dao.AppComponentSortDAO;
import org.mongodb.morphia.Datastore;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;
import org.mongodb.morphia.query.UpdateResults;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

/**
 * impl.
 * Created by zenglb on 2016/8/17.
 */
@Repository
public class AppComponentSortDAOImpl implements AppComponentSortDAO {

    @Resource(name = "ad_datastore")
    private Datastore datastore;

    @Override
    public String insertOrUpdateComponentSort(AppComponentSortDO appComponentSort) {
        UpdateOperations<AppComponentSortDO> updateOperations = datastore.createUpdateOperations(AppComponentSortDO.class);
        updateOperations.set("componentIds", appComponentSort.getComponentIds());
        updateOperations.set("gmtModified", appComponentSort.getGmtModified());
        updateOperations.setOnInsert("gmtCreate", appComponentSort.getGmtCreate());
        Query<AppComponentSortDO> query = datastore.createQuery(AppComponentSortDO.class);
        query.filter("fsUserId", appComponentSort.getFsUserId());
        query.filter("componentType", appComponentSort.getComponentType());
        UpdateResults updateResults = datastore.update(query, updateOperations, true);
        return updateResults.toString();
    }

    @Override
    public AppComponentSortDO selectOne(String fsUserId, String componentType) {
        return datastore.find(AppComponentSortDO.class)
                .filter("fsUserId = ", fsUserId)
                .filter("componentType = ", componentType)
                .limit(1).get();
    }
}




