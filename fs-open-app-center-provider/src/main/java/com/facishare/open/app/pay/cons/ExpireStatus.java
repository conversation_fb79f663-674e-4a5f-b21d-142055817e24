package com.facishare.open.app.pay.cons;

import com.facishare.open.app.pay.api.enums.CodeEnum;

/**
 * 过期状态(用来控制服务号消息发送).
 * Created by xialf on 4/14/16.
 *
 * <AUTHOR>
 */
public enum ExpireStatus implements CodeEnum {
    /**
     * 未处理的.
     */
    UNHANDLED(0),

    /**
     * 已发送.
     */
    SENT(1),

    /**
     * 发送失败.
     */
    SENT_FAILED(2),

    /**
     * 不是结束配额,不需要发送.
     */
    NOT_END(3),

    /**
     * 跳过处理(一般用在以前的数据).
     */
    SKIPPED(4);

    private final int code;

    ExpireStatus(int code) {
        this.code = code;
    }

    @Override
    public int getCode() {
        return code;
    }
}
