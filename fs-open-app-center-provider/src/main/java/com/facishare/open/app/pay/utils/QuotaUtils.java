package com.facishare.open.app.pay.utils;

import com.facishare.open.app.center.utils.GrayReleaseBiz;
import com.facishare.open.app.pay.api.cons.QuotaAttribute;
import com.facishare.open.app.pay.api.enums.QuotaType;
import com.facishare.open.app.pay.cons.ExpireStatus;
import com.facishare.open.app.pay.entity.QuotaRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Collection;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Created by xialf on 4/14/16.
 *
 * <AUTHOR>
 */
@Slf4j
public final class QuotaUtils {

    public static final int MAX_TRIAL_QUOTA = 999999;

    private QuotaUtils() {
    }

    /**
     * 判断配额是否是一个结束配额(结束配额就是没有下一个配额可以无缝接上该配额).
     *
     * @param thisRecord   目标配额
     * @param quotaRecords 配额集
     * @return 是否是结束配额
     */
    public static boolean isEndQuota(final QuotaRecord thisRecord, final Collection<QuotaRecord> quotaRecords) {
        for (final QuotaRecord quotaRecord : quotaRecords) {
            if (quotaRecord.getGmtBegin().getTime() - thisRecord.getGmtEnd().getTime() <= 1000
                    && quotaRecord.getGmtEnd().after(thisRecord.getGmtEnd())) { //1s: 开始时间和结束时间都是闭区间, 那么区间[12, 13],[14, 20]是连续的
                return false;
            }
        }
        return true;
    }

    public static boolean isValidUnhandled(final QuotaRecord quotaRecord) {
        return quotaRecord.getStatus() == 1 && quotaRecord.getExpStatus() == ExpireStatus.UNHANDLED.getCode();
    }

    /**
     * 获取最近过期的配额.
     */
    public static Optional<QuotaRecord> getLastPast(final Date now, Stream<QuotaRecord> quotaRecordStream) {
        return quotaRecordStream.filter(qr -> qr.getGmtEnd().getTime() < now.getTime())
                .max(Comparator.comparing(QuotaRecord::getGmtEnd));
    }

    /**
     * 获取任意当前有效配额.
     */
    public static Optional<QuotaRecord> getAnyCurrent(final Date now, Stream<QuotaRecord> quotaRecordStream) {
        return quotaRecordStream
                .filter(qr -> qr.getGmtBegin().getTime() <= now.getTime()
                        && qr.getGmtEnd().getTime() >= now.getTime())
                .findAny();
    }

    /**
     * 按照gmtBegin升序排列，获取第一个。若有购买和试用交叉，则返回购买.
     * @param now 现在时间
     * @param quotaRecordStream 未过期的quota
     * @return 最早的quota
     */
    public static Optional<QuotaRecord> getFirstCurrent(Date now, Stream<QuotaRecord> quotaRecordStream) {
        Stream<QuotaRecord> sortedStream = quotaRecordStream.filter(qr -> qr.getGmtBegin().getTime() <= now.getTime()
                && qr.getGmtEnd().getTime() >= now.getTime())
                .sorted((o1, o2) -> o1.getGmtBegin().compareTo(o2.getGmtBegin()));
        List<QuotaRecord> sortedRecords = sortedStream.collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(sortedRecords)) {
            for (QuotaRecord record : sortedRecords) {
                if (record.getType() == QuotaType.PURCHASE.getCode()) {
                    return Optional.of(record);
                }
            }
            return Optional.of(sortedRecords.get(0));
        }
        return Optional.empty();
    }

    /**
     * 获取下一个到期断点时间.
     */
    public static Optional<Date> getNextBreakPoint(final Date now, Stream<QuotaRecord> quotaRecordStream) {
        List<QuotaRecord> sortedQuotaRecord = quotaRecordStream.sorted(Comparator.comparing(QuotaRecord::getGmtBegin))
                .collect(Collectors.toList());

        Date nextBreakPoint = null;
        for (final QuotaRecord quotaRecord : sortedQuotaRecord) {
            if (nextBreakPoint == null) {
                if (quotaRecord.getGmtBegin().getTime() <= now.getTime()
                        && quotaRecord.getGmtEnd().getTime() >= now.getTime()) {
                    nextBreakPoint = quotaRecord.getGmtEnd();
                }
            } else {
                if (quotaRecord.getGmtBegin().getTime() <= nextBreakPoint.getTime() + 1000   //1s: 开始时间和结束时间都是闭区间, 那么区间[12, 13],[14, 20]是连续的
                        && quotaRecord.getGmtEnd().after(nextBreakPoint)) {
                    nextBreakPoint = quotaRecord.getGmtEnd();
                }
            }
        }
        return Optional.ofNullable(nextBreakPoint);
    }

    /**
     * 获取下一个未启用配额.
     */
    public static Optional<QuotaRecord> getNextUnapplied(final Date now, Stream<QuotaRecord> quotaRecordStream) {
        return quotaRecordStream.filter(qr -> qr.getGmtBegin().after(now))
                .min(Comparator.comparing(QuotaRecord::getGmtBegin));
    }

    /**
     * 获取标准配额的到期时间, 如果没有则返回null.
     */
    public static Date getExpireDateWithoutExtension(List<QuotaRecord> quotaRecords, Date now, Integer quota) {
        final List<QuotaRecord> standardRecords = quotaRecords.stream()
                .filter(rec -> rec.getAttribute().equals(QuotaAttribute.STANDARD))
                .collect(Collectors.toList());
        if (!quotaRecords.isEmpty() && standardRecords.isEmpty()) {
            log.info("All quota are extension: quotaRecords[{}], now[{}], quota[{}]", quotaRecords, now, quota);
            final Optional<Date> minOpt = quotaRecords.stream().map(QuotaRecord::getGmtBegin).min(Comparator.naturalOrder());
            if (!minOpt.isPresent()) {
                log.warn("There are no minOpt date: quotaRecords[{}]", quotaRecords);
                return null;
            } else {
                //到期时间是宽恕期开始时间的前1秒
                final LocalDateTime expireDate = LocalDateTime.ofInstant(minOpt.get().toInstant(), ZoneId.systemDefault()).minusSeconds(1);
                return Date.from(expireDate.atZone(ZoneId.systemDefault()).toInstant());
            }
        }
        if (standardRecords.isEmpty()) {
            return null;
        }
        final AvailableQuota availableQuota = AvailableQuota.valueOf(standardRecords, now);
        return availableQuota.getTheLatestQuota(standardRecords, now, quota).getExpireDate();
    }

    public static boolean useLicense(String fsEa, String appId) {
        return appId.equals(ConfigCenter.getCrmAppId()) && GrayReleaseBiz.isAllowForEa(GrayReleaseBiz.licenseQuotaQueryGrayEas, fsEa);
    }

}
