package com.facishare.open.app.pay.mapper;

import com.facishare.open.app.pay.annotation.BizDataSource;
import com.facishare.open.app.pay.entity.BizOrder;
import com.github.mybatis.mapper.ICrudMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Collection;
import java.util.List;

/**
 * Created by xialf on 4/26/16.
 *
 * <AUTHOR>
 */
@BizDataSource
public interface BizOrderMapper extends ICrudMapper<BizOrder> {
    @Select("SELECT * FROM biz_order WHERE app_id=#{appId} AND order_source=#{orderSource} AND source_order_id=#{sourceOrderId}")
    BizOrder selectOne(@Param("appId") String appId, @Param("orderSource") int orderSource,
                       @Param("sourceOrderId") String sourceOrderId);

    List<BizOrder> selectBizOrders(@Param("fsEa") String fsEa,
                                   @Param("appId") String appId);

    List<BizOrder> selectBizOrdersWithBegin(@Param("fsEa") String fsEa,
                                            @Param("appId") String appId,
                                            @Param("beginId") int beginId);

    List<BizOrder> selectBizOrdersBySource(@Param("beginId") int beginId,
                                           @Param("limit") int limit,
                                           @Param("orderSources") Collection<Integer> orderSources);
}
