package com.facishare.open.app.ad.service.impl;

import com.facishare.open.app.ad.api.enums.AppAdBannerTypeEnum;
import com.facishare.open.app.ad.api.enums.AppAdCodeEnum;
import com.facishare.open.app.ad.api.model.FsAdAppBannerDO;
import com.facishare.open.app.ad.api.service.AppCenterBannerService;
import com.facishare.open.app.ad.manager.AppCenterBannerManager;
import com.facishare.open.common.result.BaseResult;
import com.facishare.open.common.result.exception.BizException;
import com.facishare.open.common.storage.mysql.dao.Pager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date on 2016/01/08
 */
@Service("appCenterBannerServiceImpl")
public class AppCenterBannerServiceImpl implements AppCenterBannerService {
    private final Logger logger = LoggerFactory.getLogger(AppCenterBannerServiceImpl.class);

    @Autowired
    private AppCenterBannerManager appCenterBannerManager;

    @Override
    public BaseResult<List<FsAdAppBannerDO>> queryAppBanners(AppAdBannerTypeEnum appAdBannerTypeEnum) {
        try {
            List<FsAdAppBannerDO> adAppBannerDOs = appCenterBannerManager.queryAppBanners(appAdBannerTypeEnum);
            return new BaseResult<>(adAppBannerDOs);
        } catch (BizException e) {
            logger.warn("query app banners failed!", e);
            return new BaseResult<>(e);
        } catch (Exception e) {
            logger.error("query app banners failed!", e);
            return new BaseResult<>(AppAdCodeEnum.SYSTEM_EXCEPTION);
        }
    }

    @Override
    public BaseResult<Pager<FsAdAppBannerDO>> queryAppBannerPager(Pager<FsAdAppBannerDO> pager) {
        try {
            Pager<FsAdAppBannerDO> pageResult = appCenterBannerManager.queryAppBannerPager(pager);
            return new BaseResult<>(pageResult);
        } catch (BizException e) {
            logger.warn("queryAppBannerPager failed!", e);
            return new BaseResult<>(e);
        } catch (Exception e) {
            logger.error("queryAppBannerPager failed!", e);
            return new BaseResult<>(AppAdCodeEnum.SYSTEM_EXCEPTION);
        }
    }

    @Override
    public BaseResult<FsAdAppBannerDO> queryBannerById(String bannerId) {
        if (StringUtils.isEmpty(bannerId)) {
            return new BaseResult<>(AppAdCodeEnum.PARAM_ILLEGAL_EXCEPTION);
        }
        try {
            FsAdAppBannerDO result = appCenterBannerManager.queryBannerById(bannerId);
            return new BaseResult<>(result);
        } catch (BizException e) {
            logger.warn("query banner by id failed!", e);
            return new BaseResult<>(e);
        } catch (Exception e) {
            logger.error("query banner by id failed!", e);
            return new BaseResult<>(AppAdCodeEnum.SYSTEM_EXCEPTION);
        }
    }

    @Override
    public BaseResult<FsAdAppBannerDO> queryBannerByIndex(AppAdBannerTypeEnum appAdBannerTypeEnum,Integer index) {
        if (StringUtils.isEmpty(appAdBannerTypeEnum)|| null == index) {
            return new BaseResult<>(AppAdCodeEnum.PARAM_ILLEGAL_EXCEPTION);
        }
        try {
            FsAdAppBannerDO result = appCenterBannerManager.queryBannerByIndex(appAdBannerTypeEnum,index);
            return new BaseResult<>(result);
        } catch (BizException e) {
            logger.warn("query banner by id failed!", e);
            return new BaseResult<>(e);
        } catch (Exception e) {
            logger.error("query banner by id failed!", e);
            return new BaseResult<>(AppAdCodeEnum.SYSTEM_EXCEPTION);
        }
    }

    @Override
    public BaseResult<String> addBanner(FsAdAppBannerDO fsAdAppBannerDO) {
        if (null == fsAdAppBannerDO) {
            return new BaseResult<>(AppAdCodeEnum.PARAM_ILLEGAL_EXCEPTION);
        }
        try {
            String bannerId = appCenterBannerManager.addBanner(fsAdAppBannerDO);
            return new BaseResult<>(bannerId);
        } catch (BizException e) {
            logger.warn("add banners failed!", e);
            return new BaseResult<>(e);
        } catch (Exception e) {
            logger.error("add banners failed!", e);
            return new BaseResult<>(AppAdCodeEnum.SYSTEM_EXCEPTION);
        }
    }

    @Override
    public BaseResult<Void> updateBanner(FsAdAppBannerDO fsAdAppBannerDO) {
        if (null == fsAdAppBannerDO || StringUtils.isEmpty(fsAdAppBannerDO.getId())) {
            return new BaseResult<>(AppAdCodeEnum.PARAM_ILLEGAL_EXCEPTION);
        }
        try {
            appCenterBannerManager.updateBanner(fsAdAppBannerDO);
            return new BaseResult<>();
        } catch (BizException e) {
            logger.warn("add banners failed!", e);
            return new BaseResult<>(e);
        } catch (Exception e) {
            logger.error("add banners failed!", e);
            return new BaseResult<>(AppAdCodeEnum.SYSTEM_EXCEPTION);
        }
    }

    @Override
    public BaseResult<Void> deleteBanner(String bannerId) {
        if (StringUtils.isEmpty(bannerId)) {
            return new BaseResult<>(AppAdCodeEnum.PARAM_ILLEGAL_EXCEPTION);
        }
        try {
            appCenterBannerManager.deleteBanner(bannerId);
            return new BaseResult<>();
        } catch (BizException e) {
            logger.warn("delete banners failed!", e);
            return new BaseResult<>(e);
        } catch (Exception e) {
            logger.error("delete banners failed!", e);
            return new BaseResult<>(AppAdCodeEnum.SYSTEM_EXCEPTION);
        }
    }

    @Override
    public BaseResult<Void> bannerEnable(String bannerId) {
        if (StringUtils.isEmpty(bannerId)) {
            return new BaseResult<>(AppAdCodeEnum.PARAM_ILLEGAL_EXCEPTION);
        }
        try {
            appCenterBannerManager.bannerEnable(bannerId);
            return new BaseResult<>();
        } catch (BizException e) {
            logger.warn("bannerOnLine failed!", e);
            return new BaseResult<>(e);
        } catch (Exception e) {
            logger.error("bannerOnLine failed!", e);
            return new BaseResult<>(AppAdCodeEnum.SYSTEM_EXCEPTION);
        }
    }

    @Override
    public BaseResult<Void> bannerDisable(String bannerId) {
        if (StringUtils.isEmpty(bannerId)) {
            return new BaseResult<>(AppAdCodeEnum.PARAM_ILLEGAL_EXCEPTION);
        }
        try {
            appCenterBannerManager.bannerDisable(bannerId);
            return new BaseResult<>();
        } catch (BizException e) {
            logger.warn("bannerOffLine failed!", e);
            return new BaseResult<>(e);
        } catch (Exception e) {
            logger.error("bannerOffLine failed!", e);
            return new BaseResult<>(AppAdCodeEnum.SYSTEM_EXCEPTION);
        }
    }

    @Override
    public BaseResult<Void> updateBannerIndex(String bannerId, Integer index) {
        if (StringUtils.isEmpty(bannerId)) {
            return new BaseResult<>(AppAdCodeEnum.PARAM_ILLEGAL_EXCEPTION);
        }
        try {
            appCenterBannerManager.updateBannerIndex(bannerId, index);
            return new BaseResult<>();
        } catch (BizException e) {
            logger.warn("updateBannerIndex failed!", e);
            return new BaseResult<>(e);
        } catch (Exception e) {
            logger.error("updateBannerIndex failed!", e);
            return new BaseResult<>(AppAdCodeEnum.SYSTEM_EXCEPTION);
        }
    }
}
