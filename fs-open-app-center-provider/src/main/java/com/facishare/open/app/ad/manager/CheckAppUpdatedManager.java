package com.facishare.open.app.ad.manager;

import com.facishare.open.app.ad.api.enums.ModuleKeyEnum;
import com.facishare.open.common.model.FsUserVO;

import java.util.List;

/**
 * Created by zenglb on 2016/1/13.
 */
public interface CheckAppUpdatedManager {
    List<String> checkAppUpdated(FsUserVO fsUser, List<ModuleKeyEnum> modules);

    String getOrGenerateTag(FsUserVO fsUser, ModuleKeyEnum moduleKeyEnum);

    void resetTagByUserAndModuleKey(FsUserVO fsUser, ModuleKeyEnum moduleKeyEnum);

    void resetTagByModuleKey(ModuleKeyEnum moduleKeyEnum);

    void resetTagByModuleKeyAndUserIds(ModuleKeyEnum moduleKeyEnum, String fsEa, List<Integer> userIds);
}
