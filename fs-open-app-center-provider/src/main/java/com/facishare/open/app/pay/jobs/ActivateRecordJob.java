package com.facishare.open.app.pay.jobs;

import com.facishare.ibss.utils.generic.DateTimeUtils;
import com.facishare.open.app.center.api.service.OpenFsUserAppViewService;
import com.facishare.open.app.center.api.utils.JsonKit;
import com.facishare.open.app.pay.cache.QuotaCache;
import com.facishare.open.app.pay.entity.QuotaRecord;
import com.facishare.open.app.pay.mapper.QuotaRecordMapper;
import com.facishare.open.app.pay.utils.ConfigCenter;
import com.github.jedis.support.MergeJedisCmd;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;

/**
 * 激活有效的配额记录
 *
 * <AUTHOR>
 * @since on 2017/1/5.
 */
@Slf4j
@Component
public class ActivateRecordJob {

    @Resource
    private QuotaRecordMapper quotaRecordMapper;

    @Resource
    private OpenFsUserAppViewService openFsUserAppViewService;

    @Resource(name = "jedisSupport")
    private MergeJedisCmd jedis;

    @Resource
    private QuotaCache quotaCache;

    private static final String KEY = "ACTIVATE_RECORD_LOCK";

    /**
     * 激活新生效的配额.
     */
    public void activateRecord() {
        //查询前十分钟到当前新有效的crm配额记录
        log.info("activateRecord job start");
        final String setResult = jedis.set(KEY, "v", "nx", "ex", 60);

        if (setResult == null || !"OK".equalsIgnoreCase(setResult)) {
            return;
        }

        log.info("activateRecord biz start");

        Date now = new Date();
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(now);
        calendar.add(Calendar.MINUTE, -6);//6分钟前
        String dateFormat = "yyyy-MM-dd HH:mm:ss";
        String fiveMinutesAgoTime = DateTimeUtils.formatDate(calendar.getTime(), dateFormat);
        String nowString = DateTimeUtils.formatDate(now, dateFormat);

        List<QuotaRecord> quotaRecords = quotaRecordMapper
                .queryQuotaRecordsByGmtBegin(fiveMinutesAgoTime, nowString, ConfigCenter.getCrmAppId());

        log.info("activateRecord. quotaRecordMapper.queryQuotaRecordsByGmtBegin. quotaRecords[{}]",
                JsonKit.object2json(quotaRecords));

        //对这些配额记录对应的企业清除crm可见企业级缓存
        if (!CollectionUtils.isEmpty(quotaRecords)) {
            quotaRecords.forEach(quotaRecord -> {
                //清除配额缓存
                quotaCache.clear(quotaRecord.getFsEa(), ConfigCenter.getCrmAppId());
                //清除可见范围缓存
                openFsUserAppViewService
                        .cleanViewCache(quotaRecord.getFsEa(), ConfigCenter.getCrmAppId());
            });
        }


        log.info("activateRecord biz end");
    }

}
