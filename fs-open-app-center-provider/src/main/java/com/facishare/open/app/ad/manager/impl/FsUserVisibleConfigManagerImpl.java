package com.facishare.open.app.ad.manager.impl;

import com.facishare.open.app.ad.api.model.FsUserVisibleConfigDO;
import com.facishare.open.app.ad.dao.FsUserVisibleConfigDAO;
import com.facishare.open.app.ad.manager.FsUserVisibleConfigManager;
import com.facishare.open.app.ad.utils.JsonKit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * .
 * Created by zenglb on 2016/2/19.
 */
@Service
public class FsUserVisibleConfigManagerImpl implements FsUserVisibleConfigManager {
    private final Logger logger = LoggerFactory.getLogger(getClass());
    private final int USER_MAX_VISIBLE_COMPONENT_SIZE = 110;

    @Resource
    private FsUserVisibleConfigDAO fsUserVisibleConfigDAO;

    @Override
    public void saveFsUserVisibleComponentIds(String fsUserId, List<String> visibleComponentIds) {
        if (visibleComponentIds.size() >= USER_MAX_VISIBLE_COMPONENT_SIZE) { //如果超长会保存失败。限制于mysql的字段长度。现在做很流氓的限制。越过110个组件的用户全部不展示。
            logger.error("saveFsUserVisibleComponentIds failed, visibleComponentIds size too big, fsUserId[{}], visibleComponentIds[{}]", fsUserId, visibleComponentIds);
            return;
        }
        String visibleComponentIdsJson = JsonKit.object2json(visibleComponentIds);

        String visibleComponentIdsDB = fsUserVisibleConfigDAO.loadVisibleComponentIdsByFsUserId(fsUserId);
        FsUserVisibleConfigDO fsUserVisibleConfigDO = new FsUserVisibleConfigDO(fsUserId,visibleComponentIdsJson);
        if (StringUtils.isEmpty(visibleComponentIdsDB)) {
            fsUserVisibleConfigDAO.saveFsAdUserVisibleConfig(fsUserVisibleConfigDO);
            return;
        }
        fsUserVisibleConfigDAO.updateFsAdUserVisibleConfig(fsUserVisibleConfigDO);
    }

    @Override
    public List<String> loadVisibleComponentIds(String fsUserId) {
        List<String> result = null;
        String visibleComponentIds = fsUserVisibleConfigDAO.loadVisibleComponentIdsByFsUserId(fsUserId);
        if (!StringUtils.isEmpty(visibleComponentIds)) {
            result = JsonKit.json2object(visibleComponentIds, ArrayList.class);
        }
        return result;
    }

    @Override
    public void addComponent2UserVisible(String componentId, List<String> users) {
        List<FsUserVisibleConfigDO> fsUserVisibleConfigDOs = fsUserVisibleConfigDAO.loadVisibleComponentIdsByFsUsers(users);
        if(!CollectionUtils.isEmpty(fsUserVisibleConfigDOs)){
            fsUserVisibleConfigDOs.forEach(fsUserVisibleConfigDO -> {
                if (!StringUtils.isEmpty(fsUserVisibleConfigDO.getVisibleComponentIds())) {
                    List<String> visibleComponentIdList = JsonKit.json2object(fsUserVisibleConfigDO.getVisibleComponentIds(),ArrayList.class);
                    Set<String> filter = new HashSet<>();
                    Iterator<String> iterator = visibleComponentIdList.iterator();
                    while (iterator.hasNext()){
                        if(!filter.add(iterator.next())){
                            iterator.remove();
                        }
                    }
                    if(!visibleComponentIdList.contains(componentId)){
                        visibleComponentIdList.add(componentId);
                        if (visibleComponentIdList.size() >= USER_MAX_VISIBLE_COMPONENT_SIZE) { //如果超长会保存失败。限制于mysql的字段长度。现在做很流氓的限制。越过110个组件的用户全部不展示。
                            logger.error("addComponent2UserVisible failed, visibleComponentIdList size too big, componentId[{}], users[{}]", componentId, users);
                            return;
                        }
                        fsUserVisibleConfigDO.setVisibleComponentIds(JsonKit.object2json(visibleComponentIdList));
                        fsUserVisibleConfigDAO.updateFsAdUserVisibleConfig(fsUserVisibleConfigDO);
                    }
                }
            });
        }
    }
}
