package com.facishare.open.app.ad.dao;

import com.facishare.open.app.ad.api.model.FsAdBestAppDO;
import com.github.mybatis.mapper.ICrudMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 精品应用相关的数据库操作
 *
 * <AUTHOR>
 * @date on 2016/01/08
 */
public interface FsAdAppBestAppDAO extends ICrudMapper<FsAdBestAppDO> {

    /**
     * 查询指定状态的精品应用列表.
     *
     * @return
     */
    List<FsAdBestAppDO> queryBestApps();

    /**
     * 添加一个精品应用.
     *
     * @param fsAdBestAppDO
     */
    void addBestApp(FsAdBestAppDO fsAdBestAppDO);

    /**
     * 查询单个精品应用.
     *
     * @param index
     * @return
     */
    FsAdBestAppDO queryByIndex(@Param("index") Integer index);

    /**
     * 删除单个精品应用.
     *
     * @param id
     */
    void deleteBestApp(@Param("id") String id);

    /**
     * 更新排序号
     *
     * @param id
     * @param index
     */
    void updateBestAppIndex(@Param("id") String id, @Param("index") Integer index);
}
