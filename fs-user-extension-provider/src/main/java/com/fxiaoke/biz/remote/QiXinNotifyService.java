package com.fxiaoke.biz.remote;

import com.alibaba.fastjson.JSON;
import com.facishare.qixin.api.constant.SessionStatus;
import com.facishare.qixin.api.model.AuthInfo;
import com.facishare.qixin.api.model.EmployeeId;
import com.facishare.qixin.api.model.message.content.OTTemplateMessage;
import com.facishare.qixin.api.model.task.UserSessionOSS1Bean;
import com.facishare.qixin.api.service.SessionService;
import com.fxiaoke.biz.dao.entity.MenuTemplateEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class QiXinNotifyService {

    @Resource
    private SessionService sessionService;

    private static final String OSS1_SESSION_KEY = "OSS1";
    // TODO: 2019/4/10
    private static final String TEMPLATE_SESSION_KEY = "";
//    private static final String DETAIL_BUTTON_TITLE = "查看详情";
    private static final String OT_MESSAGE_TYPE = "OT";
    private static final String SESSION_TITLE = "积分变动提醒"; //ignoreI18n

    public void sendMessage(Integer enterpriseId, Integer employeeId, List<MenuTemplateEntity> entities) {

    }

    public void sendAsync(String ea, Integer receiver, String subTitle, String message, String url) {
        try {
            UserSessionOSS1Bean oss1Message = new UserSessionOSS1Bean();
            oss1Message.setEa(ea);
            oss1Message.setEmployeeIds(new ArrayList<>());
            oss1Message.getEmployeeIds().add(receiver);
            oss1Message.setCategory(OSS1_SESSION_KEY);
            oss1Message.setSubCategory(TEMPLATE_SESSION_KEY);
            oss1Message.setSessionStatus(SessionStatus.NORMAL);
            oss1Message.setNotReadCount(0);
            oss1Message.setNotReadFlag(false);
            oss1Message.setChangeOrder(true);
            oss1Message.setSticky(false);

            AuthInfo authInfo = new AuthInfo();
            authInfo.setEmployeeId(EmployeeId.build(ea, 0));
            oss1Message.setAuthInfo(authInfo);
            oss1Message.setMessageType(OT_MESSAGE_TYPE);

            OTTemplateMessage otTemplateMessage = new OTTemplateMessage();
            OTTemplateMessage.Title messageTitle = new OTTemplateMessage.Title();
            messageTitle.content = SESSION_TITLE;
            messageTitle.time = subTitle;
            otTemplateMessage.title = messageTitle;
            OTTemplateMessage.Frist messageFirst = new OTTemplateMessage.Frist();
            messageFirst.content = message;
            otTemplateMessage.first = messageFirst;
            OTTemplateMessage.Button messageButton = new OTTemplateMessage.Button();
//            messageButton.title = DETAIL_BUTTON_TITLE;
            messageButton.url = url;
            otTemplateMessage.button = messageButton;
            oss1Message.setMessageContent(JSON.toJSONString(otTemplateMessage));
            sessionService.excuteOSS1UserSession(oss1Message);
        } catch (Exception ex) {
            log.error("sendAsync message message error", ex);
        }
    }
}
