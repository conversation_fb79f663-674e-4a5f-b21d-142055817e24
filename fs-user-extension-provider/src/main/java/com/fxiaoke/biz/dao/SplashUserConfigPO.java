package com.fxiaoke.biz.dao;

import com.fxiaoke.api.enums.OSType;
import com.fxiaoke.api.model.SplashConfigArgDTO;
import com.fxiaoke.api.model.SplashConfigResultDTO;
import com.fxiaoke.api.service.VersionService;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.*;
import org.mongodb.morphia.utils.IndexType;

import java.util.Map;

@Entity(value = "SplashUserConfig", noClassnameStored = true)
@Indexes({
        @Index(
                fields = @Field(value = "versionNumber",type = IndexType.DESC),
                options = @IndexOptions(name = "VN_-1", dropDups = true)
        ),
        @Index(
                fields = {
                        @Field("startDate"),
                        @Field("endDate"),
                        @Field("startVersion"),
                        @Field("endVersion"),
                        @Field("osType"),
                        @Field("enterpriseId"),
                        @Field("userId"),
                }, options = @IndexOptions(name = "SD_1_ED_1_SV_1_EV_1_OT_1_EI_1_UI_1"))
})
public class SplashUserConfigPO {
    @Id
    private ObjectId _id;

    @Property(SplashConfigField.versionNumber)
    private long versionNumber;//版本号

    @Property(SplashConfigField.disappearType)
    private int disappearType;//消失方式,0:点击消失,>0:在指定秒数后自动消失

    @Property(SplashConfigField.showType)
    private int showType;//显示方式,0:不显示,1:启动时显示;2:进程启动和home后进入都显示

    //图片地址集合 iphone:key为1，320×480；key为2，640×960；key为3，640×1360；
    // android：  key为1，480×800；key为2，640×960；key为3，800×1280；
    @Property(SplashConfigField.picUrls)
    private Map<String, String> picUrls;

    @Property(SplashConfigField.intervalTime)
    private int intervalTime;//间隔时间,单位消失,0为不间隔

    @Property(SplashConfigField.startDate)
    private Long startDate;//有效开始时间

    @Property(SplashConfigField.endDate)
    private Long endDate;//有效结束时间

    @Property(SplashConfigField.startVersion)
    private Long startVersion;

    @Property(SplashConfigField.endVersion)
    private Long endVersion;

    @Property(SplashConfigField.isTest)
    private Boolean isTest;

    @Property(SplashConfigField.osType)
    private OSType osType;

    @Property(SplashConfigField.deleted)
    private boolean deleted;

    @Property(SplashConfigField.createTime)
    private Long createTime;

    @Property(SplashConfigField.enterpriseId)
    private Long enterpriseId;

    @Property(SplashConfigField.userId)
    private Long userId;

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(final ObjectId _id) {
        this._id = _id;
    }

    public long getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(final long versionNumber) {
        this.versionNumber = versionNumber;
    }

    public int getDisappearType() {
        return disappearType;
    }

    public void setDisappearType(final int disappearType) {
        this.disappearType = disappearType;
    }

    public int getShowType() {
        return showType;
    }

    public void setShowType(final int showType) {
        this.showType = showType;
    }

    public Map<String, String> getPicUrls() {
        return picUrls;
    }

    public void setPicUrls(final Map<String, String> picUrls) {
        this.picUrls = picUrls;
    }

    public int getIntervalTime() {
        return intervalTime;
    }

    public void setIntervalTime(final int intervalTime) {
        this.intervalTime = intervalTime;
    }

    public Long getStartDate() {
        return startDate;
    }

    public void setStartDate(final Long startDate) {
        this.startDate = startDate;
    }

    public Long getEndDate() {
        return endDate;
    }

    public void setEndDate(final Long endDate) {
        this.endDate = endDate;
    }

    public Long getStartVersion() {
        return startVersion;
    }

    public void setStartVersion(final Long startVersion) {
        this.startVersion = startVersion;
    }

    public Long getEndVersion() {
        return endVersion;
    }

    public void setEndVersion(final Long endVersion) {
        this.endVersion = endVersion;
    }

    public Boolean getTest() {
        return isTest;
    }

    public void setTest(final Boolean test) {
        isTest = test;
    }

    public OSType getOsType() {
        return osType;
    }

    public void setOsType(final OSType osType) {
        this.osType = osType;
    }

    public boolean isDeleted() {
        return deleted;
    }

    public void setDeleted(final boolean deleted) {
        this.deleted = deleted;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(final Long createTime) {
        this.createTime = createTime;
    }

    public Long getEnterpriseId() {
        return enterpriseId;
    }

    public void setEnterpriseId(final Long enterpriseId) {
        this.enterpriseId = enterpriseId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(final Long userId) {
        this.userId = userId;
    }

    /**
     * @param newConfig
     * @return
     */
    public static SplashUserConfigPO parseFrom(
            final SplashConfigArgDTO newConfig, final Long versionNumber
    ) {
        final long now = System.currentTimeMillis();
        final SplashUserConfigPO po = new SplashUserConfigPO();
        po.setDisappearType((newConfig.getDisappearType() == null) ?
                0 : newConfig.getDisappearType());
        po.setEndDate((newConfig.getEndDate() == null) ?
                Long.valueOf(Long.MAX_VALUE) : newConfig.getEndDate());
        po.setIntervalTime((newConfig.getIntervalTime() == null) ? 2 : newConfig.getIntervalTime());

        po.setOsType(newConfig.getOsType());
        po.setPicUrls(newConfig.getPicUrls());
        po.setShowType((newConfig.getShowType() == null) ? 0 : newConfig.getShowType());
        po.setStartDate(
                (newConfig.getStartDate() == null) ?
                        Long.valueOf(now) : newConfig.getStartDate());

        if (newConfig.isTest()) {
            po.setStartVersion(VersionService.getTestStartVersion(newConfig.getOsType()));
            po.setEndVersion(Long.valueOf(Long.MAX_VALUE));
        } else {
            po.setStartVersion(VersionService.getNormalStartVersion(newConfig.getOsType()));
            po.setEndVersion(VersionService.getNormalEndVersion(newConfig.getOsType()));
        }

        po.setTest(newConfig.isTest());
        po.setVersionNumber(versionNumber);
        po.setDeleted(false);
        //在5.4版,此version没有使用,而是使用开始时间作为版本号
        po.setCreateTime(now);
//        if (newConfig.getConfigType() != null) {
//            po.setPriority(
//                    Integer.valueOf((newConfig.getConfigType() == ConfigType.NORMAL) ? 0 : 1)
//            );
//        } else {
//            po.setPriority(Integer.valueOf(0));
//        }
        po.setEnterpriseId(newConfig.getEnterpriseId());
        po.setUserId(newConfig.getUserId());

        return po;
    }

    public SplashConfigResultDTO toDTO() {
        final SplashConfigResultDTO dto = new SplashConfigResultDTO();
        dto.setCreateTime(createTime);
        dto.setDisappearType(disappearType);
        dto.setEndDate(endDate);
        dto.setIntervalTime(intervalTime);
        dto.setOsType(osType);
        dto.setPicUrls(picUrls);
//        dto.setPriority(priority);
        dto.setShowType(showType);
        dto.setStartDate(startDate);
        dto.setTest(isTest);
        dto.setVersionNumber(versionNumber);
        dto.setDeleted(deleted);

        return dto;
    }
}
