package com.fxiaoke.biz.dao.entity;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.api.model.PageSettings;
import com.google.common.collect.Lists;

import com.fxiaoke.api.model.PageData;
import com.fxiaoke.api.model.type.MenuType;
import com.fxiaoke.biz.dao.entity.property.PageDataEntityProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.mongodb.morphia.annotations.Embedded;
import org.mongodb.morphia.annotations.Property;

import java.util.List;

/**
 * 页面数据
 *
 * <AUTHOR>
 */
@Embedded
@Data
@Slf4j
public class PageDataEntity {
    /**
     * 页面标题
     */
    @Property(PageDataEntityProperty.TITLE)
    private String title;
    /**
     * 数据类型
     *
     * @see MenuType
     */
    @Property(PageDataEntityProperty.MENU_TYPE)
    private int menuType;

    /**
     * 当 menuType == {@link MenuType#PAGE_H5} 时
     * H5 url
     */
    @Property(PageDataEntityProperty.URL)
    private String url;

    @Embedded(PageDataEntityProperty.PAGE_DATA_BLOCKS)
    private List<PageDataBlockEntity> pageDataBlocks;

    public static PageData convert2Dto(PageDataEntity data) {
        PageData pageData = new PageData();
        pageData.setTitle(data.title);
        pageData.setMenuType(data.menuType);
        pageData.setUrl(data.url);
        pageData.setPageDataBlocks(PageDataBlockEntity.convert2Dto(data.pageDataBlocks));

        return pageData;
    }

    public static PageDataEntity convert2Entity(PageData data) {
        PageDataEntity pageDataEntity = new PageDataEntity();
        pageDataEntity.setTitle(data.getTitle());
        pageDataEntity.setMenuType(data.getMenuType());
        pageDataEntity.setUrl(data.getUrl());
        pageDataEntity.setPageDataBlocks(PageDataBlockEntity.convert2Entity(data.getPageDataBlocks()));

        return pageDataEntity;
    }

    public static PageSettings convertPageSettings(String pageSettingsStr) {
        try {
            return JSONObject.parseObject(pageSettingsStr, PageSettings.class);
        } catch (Exception e) {
            log.error("convertPageSettings error ! pageSettingStr:{}", pageSettingsStr, e);
            return null;
        }
    }
}
