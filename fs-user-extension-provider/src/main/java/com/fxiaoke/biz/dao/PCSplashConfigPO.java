package com.fxiaoke.biz.dao;

import com.fxiaoke.api.model.PCSplashConfigArgDTO;
import com.fxiaoke.api.model.PCSplashConfigResultDTO;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.*;
import org.mongodb.morphia.utils.IndexType;

@Entity(value = "PCSplashConfig", noClassnameStored = true)
@Indexes({
        @Index(
                fields = @Field(value = "versionNumber",type = IndexType.DESC),
                options = @IndexOptions(name = "VN_-1", dropDups = true)
        ),
        @Index(
                fields = {
                        @Field("startDate"),
                        @Field("endDate"),
                }, options = @IndexOptions(name = "SD_1_ED_1"))
})
public class PCSplashConfigPO {
    @Id
    private ObjectId _id;

    @Property(PCSplashConfigField.versionNumber)
    private long versionNumber;//版本号

    @Property(PCSplashConfigField.picUrl)
    private String picUrl;

    @Property(PCSplashConfigField.startDate)
    private Long startDate;//有效开始时间

    @Property(PCSplashConfigField.endDate)
    private Long endDate;//有效结束时间

    @Property(PCSplashConfigField.deleted)
    private boolean deleted;

    @Property(PCSplashConfigField.createTime)
    private Long createTime;

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(final ObjectId _id) {
        this._id = _id;
    }

    public long getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(final long versionNumber) {
        this.versionNumber = versionNumber;
    }

    public String getPicUrl() {
        return picUrl;
    }

    public void setPicUrl(final String picUrl) {
        this.picUrl = picUrl;
    }

    public Long getStartDate() {
        return startDate;
    }

    public void setStartDate(final Long startDate) {
        this.startDate = startDate;
    }

    public Long getEndDate() {
        return endDate;
    }

    public void setEndDate(final Long endDate) {
        this.endDate = endDate;
    }

    public boolean isDeleted() {
        return deleted;
    }

    public void setDeleted(final boolean deleted) {
        this.deleted = deleted;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(final Long createTime) {
        this.createTime = createTime;
    }

    /**
     * @param newConfig
     * @return
     */
    public static PCSplashConfigPO parseFrom(
            final PCSplashConfigArgDTO newConfig, final Long versionNumber
    ) {
        final long now = System.currentTimeMillis();
        final PCSplashConfigPO po = new PCSplashConfigPO();
        po.setEndDate((newConfig.getEndDate() == null) ?
                Long.valueOf(Long.MAX_VALUE) : newConfig.getEndDate());
        po.setPicUrl(newConfig.getPicUrl());
        po.setStartDate(
                (newConfig.getStartDate() == null) ?
                        Long.valueOf(now) : newConfig.getStartDate());

        po.setVersionNumber(versionNumber);
        po.setDeleted(false);
        //在5.4版,此version没有使用,而是使用开始时间作为版本号
        po.setCreateTime(now);
        return po;
    }

    public PCSplashConfigResultDTO toDTO() {
        final PCSplashConfigResultDTO dto = new PCSplashConfigResultDTO();
        dto.setCreateTime(createTime);
        dto.setEndDate(endDate);
        dto.setStartDate(startDate);
        dto.setVersionNumber(versionNumber);
        dto.setDeleted(deleted);
        dto.setPicUrl(picUrl);

        return dto;
    }
}
