package com.fxiaoke.biz.dao.entity;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.webpage.customer.api.constant.CustomerLayoutField;
import com.fxiaoke.api.model.PageTemplate;
import com.fxiaoke.api.model.type.PageTemplateStatus;
import com.fxiaoke.api.model.type.TemplateOperationAuth;
import com.fxiaoke.biz.dao.entity.property.PageTemplateEntityProperty;
import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Entity(value = "PageTemplateV2", noClassnameStored = true)
@Indexes({
        @Index(fields = {
                @Field(PageTemplateEntityProperty.ENTERPRISE_ID)
                , @Field(PageTemplateEntityProperty.PAGE_TEMPLATE_ID)
        }
                , options = @IndexOptions(unique = true, dropDups = true, background = true)),
        @Index(fields = {
                @Field(PageTemplateEntityProperty.ENTERPRISE_ID)
                , @Field(PageTemplateEntityProperty.NOTIFY_IDS)
        }
                , options = @IndexOptions(background = true)),
//        校验引用 , 无法使用,数据量太大
//        @Index(fields = {
//                @Field(PageTemplateEntityProperty.ENTERPRISE_ID)
//                , @Field(PageTemplateEntityProperty.DATA + "." + PageDataEntityProperty.PAGE_DATA_BLOCKS + "." + PageDataBlockEntityProperty.BLOCK_DATA)
//        }
//                , options = @IndexOptions(background = true)),
})
@Data
/**
 * 自定义页面
 * <AUTHOR>
 */ public class PageTemplateEntity implements com.facishare.qixin.sysdb.model.Data {
    @Id
    private ObjectId id;
    @Property(PageTemplateEntityProperty.PAGE_TEMPLATE_ID)
    private String pageTemplateId;
    @Property(PageTemplateEntityProperty.ENTERPRISE_ID)
    private Integer enterpriseId;
    /**
     * 页面模板名称
     */
    @Property(PageTemplateEntityProperty.NAME)
    private String name;

    /**
     * 普通图标(自定义页面,下拉框 使用)
     */
    @Property(PageTemplateEntityProperty.ICON)
    private String icon;
    /**
     * 未选中图标
     */
    @Property(PageTemplateEntityProperty.BACKGROUND)
    private String background;
    /**
     * 选中图标
     */
    @Property(PageTemplateEntityProperty.SELECT_ICON)
    private String selectIcon;

    /**
     * 需要通知的应用id
     */
    @Property(PageTemplateEntityProperty.NOTIFY_IDS)
    private List<String> notifyIds;

    /**
     * 数据可操作权限
     *
     * @see TemplateOperationAuth
     */
    @Property(PageTemplateEntityProperty.OPERATION_AUTH)
    private Integer operationAuth;
    /**
     * 整个页面数据
     */
    @Embedded(PageTemplateEntityProperty.DATA)
    private PageDataEntity data;

    @Property(PageTemplateEntityProperty.CREATE_ID)
    private Integer createId;
    @Property(PageTemplateEntityProperty.CREATE_TIME)
    private Long createTime;
    @Property(PageTemplateEntityProperty.UPDATE_EMPLOYEE_ID)
    private Integer updateEmployeeId;
    @Property(PageTemplateEntityProperty.UPDATE_TIME)
    private Long updateTime;

    /**
     * 是否有效
     *
     * @see PageTemplateStatus
     */
    @Property(PageTemplateEntityProperty.STATUS)
    private Integer status;

    /**
     * 真实的修改时间
     */
    @Property(PageTemplateEntityProperty.REAL_UPDATE_TIME)
    private String realUpdateTime;

    /**
     * 来源类型:
     * 自建 : null,
     * preset : preset,
     * license : license_xxxx
     */
    @Property(PageTemplateEntityProperty.SOURCE_TYPE)
    private String sourceType;

    /**
     * 拷贝的原id
     */
    @Property(PageTemplateEntityProperty.SOURCE_ID)
    private String sourceId;

    @Property(PageTemplateEntityProperty.ICON_TYPE)
    private int iconType;

    @Property(PageTemplateEntityProperty.LAYOUT)
    private String layout;
    /**
     * 布局的版本：目的是为了区分互联筛选器
     * 100：没有筛选器的版本
     * 200：有筛选器
     */
    @Property(PageTemplateEntityProperty.LAYOUT_VERSION)
    private Integer layoutVersion = 200;

    /**
     * 布局类型
     * 0：单页面布局
     * 1：底导航布局
     */
    @Property(PageTemplateEntityProperty.LAYOUT_TYPE)
    private Integer layoutType = 0;
    /**
     * 底导航布局数据存储，日后可对其他布局数据进行存储，格式是JSONObject
     */
    @Property(PageTemplateEntityProperty.LAYOUT_EXP)
    private String layoutExp;

    /**
     * 顶导航开启 1开启  0未开启
     */
    @Property(PageTemplateEntityProperty.PAGE_MULTI_TYPE)
    private int pageMultiType;

    /**
     * 默认标签页index
     */
    @Property(PageTemplateEntityProperty.DEFAULT_LABEL_INDEX)
    private int defaultLabelIndex;

    /**
     * 所有标签页数据信息，layoutList"，此项考虑和customerLayout冗余，customerLayout作为往前兼容的冗余数据
     */
    @Property(PageTemplateEntityProperty.LAYOUT_LIST)
    private List<String> layoutList;

    /**
     * 自定义页面备注字段
     */
    @Property(PageTemplateEntityProperty.DESCRIPTION)
    private String description;

    /**
     * 布局样式  0方角   1圆角   默认0方角
     */
    @Property(PageTemplateEntityProperty.LAYOUT_STYLE_TYPE)
    private int layoutStyleType;
    @Property(PageTemplateEntityProperty.FROM_OLD_CRM_HOME)
    private Boolean fromOldCrmHomePage = false;

    /**
     * appId
     */
    @Property(PageTemplateEntityProperty.APP_ID)
    private String appId;

    /**
     * applyType     applyType==null/0：企业内   applyType==1：互联的
     */
    @Property(PageTemplateEntityProperty.APPLY_TYPE)
    private Integer applyType;

    @Property(PageTemplateEntityProperty.PAGE_SETTINGS)
    private String pageSettings;


    public static PageTemplate convert2Dto(PageTemplateEntity entity) {
        if (Objects.isNull(entity)) {
            return null;
        }
        PageTemplate pageTemplate = new PageTemplate();
        pageTemplate.setName(entity.name);
        pageTemplate.setIcon(entity.icon);
        pageTemplate.setBackground(entity.background);
        pageTemplate.setSelectIcon(entity.selectIcon);
        pageTemplate.setPageTemplateId(entity.pageTemplateId);
        pageTemplate.setEnterpriseId(entity.enterpriseId);
        pageTemplate.setCreateId(entity.createId);
        pageTemplate.setCreateTime(entity.createTime);
        pageTemplate.setUpdateEmployeeId(entity.updateEmployeeId);
        pageTemplate.setUpdateTime(entity.updateTime);
        pageTemplate.setOperationAuth(entity.operationAuth);
        pageTemplate.setSourceType(entity.sourceType);
        pageTemplate.setSourceId(entity.sourceId);
        pageTemplate.setIconType(entity.getIconType());
        pageTemplate.setAppId(entity.getAppId());
        pageTemplate.setApplyType(entity.getApplyType());
        if (StringUtils.isNotEmpty(entity.getPageSettings())){
            pageTemplate.setPageSettings(PageDataEntity.convertPageSettings(entity.getPageSettings()));
        }

        pageTemplate.setData(PageDataEntity.convert2Dto(entity.getData()));
        pageTemplate.setLayout(entity.getLayout());
        if (entity.getLayoutVersion() == null) {
            pageTemplate.setLayoutVersion(100);
        } else {
            pageTemplate.setLayoutVersion(entity.getLayoutVersion());
        }
        pageTemplate.setLayoutType(entity.getLayoutType());
        pageTemplate.setLayoutExp(entity.getLayoutExp());

        // 修复 layoutList
        List<String> fixedLayoutList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(entity.getLayoutList())) {
            fixedLayoutList = entity.getLayoutList().stream().map(layout -> {
                JSONObject obj = JSON.parseObject(layout);
                if (Objects.isNull(obj)) {
                    return null;
                }
                if (StringUtils.isBlank(obj.getString(CustomerLayoutField.LABEL_PAGE_NAME))) {
                    obj.put(CustomerLayoutField.LABEL_PAGE_NAME, CustomerLayoutField.LABEL_PAGE_NAME_CN);
                }
                if (StringUtils.isBlank(obj.getString(CustomerLayoutField.LABEL_INDEX))) {
                    obj.put(CustomerLayoutField.LABEL_INDEX, CustomerLayoutField.LABEL_INDEX_STRING);
                }
                return obj.toJSONString();
            }).filter(Objects::nonNull).collect(Collectors.toList());

        } else { // layoutList 为空时, layout -> layoutList
            JSONObject obj = JSON.parseObject(entity.getLayout());
            if (null != obj) {
                if (StringUtils.isBlank(obj.getString(CustomerLayoutField.LABEL_PAGE_NAME))) {
                    obj.put(CustomerLayoutField.LABEL_PAGE_NAME, CustomerLayoutField.LABEL_PAGE_NAME_CN);
                }
                // 设置 tab index
                obj.put(CustomerLayoutField.LABEL_INDEX, CustomerLayoutField.LABEL_INDEX_STRING);
                fixedLayoutList = Lists.newArrayList(obj.toJSONString());
            }
        }
        pageTemplate.setLayoutList(fixedLayoutList);
        pageTemplate.setPageMultiType(entity.getPageMultiType());
        pageTemplate.setDefaultLabelIndex(entity.getDefaultLabelIndex());
        if (StringUtils.isNotEmpty(entity.getDescription())) {
            pageTemplate.setDescription(entity.getDescription());
        }
        pageTemplate.setLayoutStyleType(entity.getLayoutStyleType());
        pageTemplate.setFromOldCrmHomePage(entity.getFromOldCrmHomePage());
        return pageTemplate;
    }

    public static List<PageTemplate> convert2Dto(List<PageTemplateEntity> entities) {
        if (CollectionUtils.isEmpty(entities)) {
            return new ArrayList<>();
        }
        return entities.stream().map(PageTemplateEntity::convert2Dto).collect(Collectors.toList());
    }

    @Override
    public String getDataId() {
        return this.pageTemplateId;
    }

    @Override
    public void setDataId(String s) {
        this.pageTemplateId = s;
    }

    @Override
    public int getTenantId() {
        return this.enterpriseId;
    }

    @Override
    public void setTenantId(int i) {
        this.enterpriseId = i;
    }
}
