package com.fxiaoke.biz.dao;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.api.model.SplashPictureItemDto;
import com.fxiaoke.api.model.type.SplashPictureStatus;
import com.fxiaoke.biz.dao.entity.SplashPictureEntity;
import com.fxiaoke.biz.dao.entity.property.SplashPictureEntityProperty;
import com.google.common.collect.ImmutableMap;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Objects;
import java.util.UUID;

@Repository
public class SplashPictureDao extends BaseDao<SplashPictureEntity> {

    private Query<SplashPictureEntity> createQuery(Integer enterpriseId) {
        return createQuery(ImmutableMap.of(
                SplashPictureEntityProperty.ENTERPRISE_ID, enterpriseId,
                SplashPictureEntityProperty.STATUS, SplashPictureStatus.NORMAL));
    }

    /**
     * 删除自定义闪屏图
     * @param enterpriseId
     * @param currentEmployeeId
     */
    public void deleteSplashPicture(Integer enterpriseId, Integer currentEmployeeId) {
        Query<SplashPictureEntity> query = createQuery(enterpriseId);

        UpdateOperations<SplashPictureEntity> update = createUpdate(ImmutableMap.of(
                SplashPictureEntityProperty.UPDATE_EMPLOYEE_ID, currentEmployeeId,
                SplashPictureEntityProperty.UPDATE_TIME, System.currentTimeMillis(),
                SplashPictureEntityProperty.STATUS, SplashPictureStatus.DELETE
        ));

        datastore.update(query, update);
    }

    /**
     * 更新自定义闪屏图
     * @param enterpriseId
     * @param currentEmployeeId
     * @param duration
     * @param url
     */
    public void createOrUpdateSplashPicture(Integer enterpriseId,
                                            Integer currentEmployeeId,
                                            Long duration,
                                            String url,
                                            List<SplashPictureItemDto> pictureInfos) {
        String id = enterpriseId + "-" + UUID.randomUUID().toString();

        Query<SplashPictureEntity> query = createQuery(enterpriseId);
        UpdateOperations<SplashPictureEntity> update = createUpdate(ImmutableMap.of(
                SplashPictureEntityProperty.UPDATE_EMPLOYEE_ID, currentEmployeeId,
                SplashPictureEntityProperty.SPLASH_PICTURE_ID, id,
                SplashPictureEntityProperty.UPDATE_TIME, System.currentTimeMillis(),
                SplashPictureEntityProperty.DURATION, duration
        ));
        //这俩字段灰度期间互斥
        if (Objects.nonNull(url)) {
            update.set(SplashPictureEntityProperty.IMAGE_URL, url);
        }
        if (Objects.nonNull(pictureInfos)) {
            update.set(SplashPictureEntityProperty.PICTURE_INFO, JSONObject.toJSONString(pictureInfos));
        }
        update.set(SplashPictureEntityProperty.STATUS, SplashPictureStatus.NORMAL);
        datastore.findAndModify(query, update, false, true);
    }

    /**
     * 获取企业自定义闪屏图
     * @param enterpriseId
     * @return
     */
    public List<SplashPictureEntity> getSplashPicture(Integer enterpriseId) {
        return createQuery(enterpriseId).asList();
    }
}
