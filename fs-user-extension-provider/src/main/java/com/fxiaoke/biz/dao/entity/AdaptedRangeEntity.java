package com.fxiaoke.biz.dao.entity;

import com.fxiaoke.api.model.MenuTemplateRange;
import com.fxiaoke.biz.dao.entity.property.AdaptedRangeEntityProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.mongodb.morphia.annotations.Embedded;
import org.mongodb.morphia.annotations.Property;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 适用范围
 *
 * <AUTHOR>
 */
@Embedded
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AdaptedRangeEntity {
    @Property(AdaptedRangeEntityProperty.EMPLOYEE_IDS)
    private List<Integer> employeeIds;
    @Property(AdaptedRangeEntityProperty.DEPARTMENT_IDS)
    private List<Integer> departmentIds;
    @Property(AdaptedRangeEntityProperty.ROLE_IDS)
    private List<String> roleIds;
    @Property(AdaptedRangeEntityProperty.USER_GROUP_IDS)
    private List<String> userGroupIds;

    public static MenuTemplateRange convert2Dto(AdaptedRangeEntity range) {
        if (Objects.isNull(range)) {
            return new MenuTemplateRange(new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), new ArrayList<>());
        }

        MenuTemplateRange menuTemplateRange = new MenuTemplateRange();
        menuTemplateRange.setDepartmentIds(CollectionUtils.isEmpty(range.departmentIds) ? new ArrayList<>() : range.departmentIds);
        menuTemplateRange.setEmployeeIds(CollectionUtils.isEmpty(range.employeeIds) ? new ArrayList<>() : range.employeeIds);
        menuTemplateRange.setRoleIds(CollectionUtils.isEmpty(range.roleIds) ? new ArrayList<>() : range.roleIds);
        menuTemplateRange.setUserGroupIds(CollectionUtils.isEmpty(range.userGroupIds) ? new ArrayList<>() : range.userGroupIds);
        return menuTemplateRange;
    }

    public static AdaptedRangeEntity convert2Entity(MenuTemplateRange range) {
        AdaptedRangeEntity adaptedRangeEntity = new AdaptedRangeEntity();
        adaptedRangeEntity.setEmployeeIds(range.getEmployeeIds());
        adaptedRangeEntity.setDepartmentIds(range.getDepartmentIds());
        adaptedRangeEntity.setRoleIds(range.getRoleIds());
        adaptedRangeEntity.setUserGroupIds(range.getUserGroupIds());

        return adaptedRangeEntity;
    }
}
