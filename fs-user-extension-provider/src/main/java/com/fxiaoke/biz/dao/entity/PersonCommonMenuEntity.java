package com.fxiaoke.biz.dao.entity;

import com.facishare.qixin.datastore.BaseEntity;
import lombok.Data;
import lombok.ToString;
import org.mongodb.morphia.annotations.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/2/24 6:17 PM
 */
//TODO
@Entity(value = "PersonCommonMenuEntity", noClassnameStored = true)
@Indexes({
        @Index(fields = {@Field("EId"), @Field("userId"), @Field("pageTemplateId"), @Field("menuGroupApiName")},
                options = @IndexOptions(name = "EId_1_userId_1_pageTemplateId_1_menuGroupApiName_1", background = true))
})
@Data
@ToString(callSuper = true)
public class PersonCommonMenuEntity extends BaseEntity {
    @Property("userId")
    private String userId;
    @Property("pageTemplateId")
    private String pageTemplateId;
    @Property("menuGroupApiName")
    private String menuGroupApiName;
    @Embedded("menuItemList")
    private List<MenuItem> menuItemList;
    @Property("updateTime")
    private long updateTime;


}
