package com.fxiaoke.biz.dao;

import com.fxiaoke.api.model.CheckDuplicateRange;
import com.fxiaoke.api.model.MenuTemplate;
import com.fxiaoke.api.model.type.MenuTemplateStatus;
import com.fxiaoke.api.model.type.MenuType;
import com.fxiaoke.api.model.type.PlatformType;
import com.fxiaoke.api.model.type.TemplateOperationAuth;
import com.fxiaoke.biz.dao.entity.AdaptedRangeEntity;
import com.fxiaoke.biz.dao.entity.MenuEntity;
import com.fxiaoke.biz.dao.entity.MenuTemplateEntity;
import com.fxiaoke.biz.dao.entity.property.AdaptedRangeEntityProperty;
import com.fxiaoke.biz.dao.entity.property.MenuEntityProperty;
import com.fxiaoke.biz.dao.entity.property.MenuTemplateEntityProperty;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mongodb.morphia.Datastore;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.*;

@Repository
public class MenuTemplateDao extends BaseDao<MenuTemplateEntity> {
    @Resource
    private Datastore datastore;

    private Query<MenuTemplateEntity> createQuery(int enterpriseId) {
        return createQuery(ImmutableMap.of(MenuTemplateEntityProperty.ENTERPRISE_ID, enterpriseId, MenuTemplateEntityProperty.STATUS, MenuTemplateStatus.NORMAL));
    }

    private Query<MenuTemplateEntity> createQuery(int enterpriseId, Map<String, Object> value) {
        Query<MenuTemplateEntity> query = createQuery(enterpriseId);
        value.forEach((key, v) -> query.field(key).equal(v));
        return query;
    }

    private Query<MenuTemplateEntity> createQuery(int enterpriseId, String id) {
        return createQuery(ImmutableMap.of(MenuTemplateEntityProperty.ENTERPRISE_ID, enterpriseId, MenuTemplateEntityProperty.MENU_TEMPLATE_ID, id, MenuTemplateEntityProperty.STATUS, MenuTemplateStatus.NORMAL));
    }

//    private UpdateOperations<MenuTemplateEntity> createUpdate(Integer employeeId) {
//        long time = System.currentTimeMillis();
//        return createUpdate(employeeId, time);
//    }

    private UpdateOperations<MenuTemplateEntity> createUpdate(Integer employeeId, Long time) {
        UpdateOperations<MenuTemplateEntity> update = createUpdate();
        if (Objects.nonNull(employeeId)) {
            update.set(MenuTemplateEntityProperty.UPDATE_EMPLOYEE_ID, employeeId);
        }
        if (Objects.nonNull(time)) {
            update.set(MenuTemplateEntityProperty.UPDATE_TIME, time);
        }
        return update;
    }

    private UpdateOperations<MenuTemplateEntity> createUpdate(Integer employeeId, Map<String, Object> updateValue, Long time) {
        UpdateOperations<MenuTemplateEntity> update = createUpdate(employeeId, time);
        updateValue.forEach(update::set);
        return update;
    }

    @Override
    public UpdateOperations<MenuTemplateEntity> createUpdate() {
        UpdateOperations<MenuTemplateEntity> update = super.createUpdate();
        update.set(MenuTemplateEntityProperty.REAL_UPDATE_TIME, System.currentTimeMillis());
        return update;
    }

    public List<MenuTemplateEntity> queryVendorApp() {
        Query<MenuTemplateEntity> query = createQuery();
        query.field("menus.value").equal("relationVendor");
        return query.asList();
    }

    public void delete(int enterpriseId, List<String> ids) {
        Query<MenuTemplateEntity> query = createQuery(enterpriseId);
        query.field(MenuTemplateEntityProperty.MENU_TEMPLATE_ID).in(ids);
        UpdateOperations<MenuTemplateEntity> update = createUpdate();
        update.set(MenuTemplateEntityProperty.STATUS, MenuTemplateStatus.DELETE);
        update.set(MenuTemplateEntityProperty.EFFECTIVE_EMPLOYEE_IDS, new ArrayList<>());
        datastore.update(query, update);
    }

    public void delete(int enterpriseId) {
        Query<MenuTemplateEntity> query = createQuery(enterpriseId);
        datastore.delete(query);
    }

    public MenuTemplateEntity delete(int enterpriseId, Integer employeeId, String id, Long time) {
        Query<MenuTemplateEntity> query = createQuery(enterpriseId, id);
        UpdateOperations<MenuTemplateEntity> update = createUpdate(employeeId, time);
        update.set(MenuTemplateEntityProperty.STATUS, MenuTemplateStatus.DELETE);
//        删除的时候不删除有效员工,启用时删除
//        update.set(MenuTemplateEntityProperty.EFFECTIVE_EMPLOYEE_IDS, new ArrayList<>());
        return datastore.findAndModify(query, update, true);
    }

    public void create(MenuTemplateEntity entity) {
        datastore.save(entity);
    }

    public MenuTemplateEntity save(MenuTemplateEntity entity) {
        Query<MenuTemplateEntity> query = createQuery();
        query.field(MenuTemplateEntityProperty.ENTERPRISE_ID).equal(entity.getEnterpriseId());
        query.field(MenuTemplateEntityProperty.MENU_TEMPLATE_ID).equal(entity.getMenuTemplateId());

        UpdateOperations<MenuTemplateEntity> update = createUpdate();

        update.set(MenuTemplateEntityProperty.ENTERPRISE_ID, entity.getEnterpriseId());
        update.set(MenuTemplateEntityProperty.MENU_TEMPLATE_ID, entity.getMenuTemplateId());
        update.set(MenuTemplateEntityProperty.NAME, entity.getName() == null ? "" : entity.getName());
        update.set(MenuTemplateEntityProperty.CREATE_TIME, entity.getCreateTime());
        update.set(MenuTemplateEntityProperty.CREATE_ID, entity.getCreateId());
        update.set(MenuTemplateEntityProperty.UPDATE_EMPLOYEE_ID, entity.getUpdateEmployeeId());
        update.set(MenuTemplateEntityProperty.UPDATE_TIME, entity.getUpdateTime());
        update.set(MenuTemplateEntityProperty.REAL_UPDATE_TIME, entity.getRealUpdateTime() == null ? "" : entity.getRealUpdateTime());
        if (Objects.nonNull(entity.getRange())) {
            update.set(MenuTemplateEntityProperty.RANGE, entity.getRange());
        }
        update.set(MenuTemplateEntityProperty.DEFAULT_MENU_INDEX, entity.getDefaultMenuIndex() == null ? 0 : entity.getDefaultMenuIndex());
        if (Objects.nonNull(entity.getMenus())) {
            update.set(MenuTemplateEntityProperty.MENUS, entity.getMenus());
        }

        update.set(MenuTemplateEntityProperty.EFFECTIVE_EMPLOYEE_IDS, CollectionUtils.isEmpty(entity.getEffectiveEmployeeIds()) ? new ArrayList<>() : entity.getEffectiveEmployeeIds());
        update.set(MenuTemplateEntityProperty.EXCLUDE_EMPLOYEE_IDS, CollectionUtils.isEmpty(entity.getExcludeEmployeeIds()) ? new ArrayList<>() : entity.getExcludeEmployeeIds());
        update.set(MenuTemplateEntityProperty.PLATFORM, entity.getPlatform());
        update.set(MenuTemplateEntityProperty.STATUS, entity.getStatus());
        if (Objects.nonNull(entity.getSourceType())) {
            update.set(MenuTemplateEntityProperty.SOURCE_TYPE, entity.getSourceType());
        }
        if (Objects.nonNull(entity.getSourceId())) {
            update.set(MenuTemplateEntityProperty.SOURCE_ID, entity.getSourceId());
        }
        if (Objects.nonNull(entity.getOperationAuth())) {
            update.set(MenuTemplateEntityProperty.OPERATION_AUTH, entity.getOperationAuth());
        }
        //沙盒复制新动作
        if (Objects.nonNull(entity.getScopeList())) {
            update.set(MenuTemplateEntityProperty.SCOPE_LIST, entity.getScopeList());
        }
        update.set(MenuTemplateEntityProperty.PRIORITY, entity.getPriorityLevel());


        return datastore.findAndModify(query, update, true, true);
    }

    public MenuTemplateEntity preset(MenuTemplateEntity entity, Integer employeeId, boolean coverRange) {
        Query<MenuTemplateEntity> query = createQuery();
        query.field(MenuTemplateEntityProperty.ENTERPRISE_ID).equal(entity.getEnterpriseId());
        query.field(MenuTemplateEntityProperty.MENU_TEMPLATE_ID).equal(entity.getMenuTemplateId());

        UpdateOperations<MenuTemplateEntity> update = createUpdate();

        long time = System.currentTimeMillis();
        update.set(MenuTemplateEntityProperty.ENTERPRISE_ID, entity.getEnterpriseId());
        update.set(MenuTemplateEntityProperty.NAME, entity.getName() == null ? "" : entity.getName());
        update.set(MenuTemplateEntityProperty.CREATE_TIME, time);
        update.set(MenuTemplateEntityProperty.CREATE_ID, employeeId);
        update.set(MenuTemplateEntityProperty.UPDATE_EMPLOYEE_ID, employeeId);
        update.set(MenuTemplateEntityProperty.UPDATE_TIME, time);

        update.set(MenuTemplateEntityProperty.DEFAULT_MENU_INDEX, entity.getDefaultMenuIndex() == null ? 0 : entity.getDefaultMenuIndex());
        update.set(MenuTemplateEntityProperty.MENUS, entity.getMenus());

        update.set(MenuTemplateEntityProperty.STATUS, entity.getStatus());

        if (Objects.nonNull(entity.getSourceType())) {
            update.set(MenuTemplateEntityProperty.SOURCE_TYPE, entity.getSourceType());
        }
        if (Objects.nonNull(entity.getSourceId())) {
            update.set(MenuTemplateEntityProperty.SOURCE_ID, entity.getSourceId());
        }
        if (Objects.nonNull(entity.getOperationAuth())) {
            update.set(MenuTemplateEntityProperty.OPERATION_AUTH, entity.getOperationAuth());
        }

        if (Objects.isNull(entity.getRange())) {
            entity.setRange(new AdaptedRangeEntity(Lists.newArrayList(),
                    Lists.newArrayList(),
                    Lists.newArrayList(),
                    Lists.newArrayList()));
        }
        if (Objects.isNull(entity.getRange().getEmployeeIds())) {
            entity.getRange().setEmployeeIds(Lists.newArrayList());
        }
        if (Objects.isNull(entity.getRange().getRoleIds())) {
            entity.getRange().setRoleIds(Lists.newArrayList());
        }
        if (Objects.isNull(entity.getRange().getDepartmentIds())) {
            entity.getRange().setDepartmentIds(Lists.newArrayList());
        }
        if (Objects.isNull(entity.getRange().getUserGroupIds())) {
            entity.getRange().setUserGroupIds(Lists.newArrayList());
        }
        entity.setEffectiveEmployeeIds(CollectionUtils.isEmpty(entity.getEffectiveEmployeeIds()) ? new ArrayList<>() : entity.getEffectiveEmployeeIds());
        entity.setScopeList(CollectionUtils.isEmpty(entity.getScopeList()) ? new ArrayList<>() : entity.getScopeList());

        Map<String, List<?>> scopeApplication = new HashMap<>();
        scopeApplication.put(MenuTemplateEntityProperty.RANGE + "." + AdaptedRangeEntityProperty.EMPLOYEE_IDS, entity.getRange().getEmployeeIds());
        scopeApplication.put(MenuTemplateEntityProperty.RANGE + "." + AdaptedRangeEntityProperty.ROLE_IDS, entity.getRange().getRoleIds());
        scopeApplication.put(MenuTemplateEntityProperty.RANGE + "." + AdaptedRangeEntityProperty.DEPARTMENT_IDS, entity.getRange().getDepartmentIds());
        scopeApplication.put(MenuTemplateEntityProperty.RANGE + "." + AdaptedRangeEntityProperty.USER_GROUP_IDS, entity.getRange().getUserGroupIds());
        scopeApplication.put(MenuTemplateEntityProperty.EFFECTIVE_EMPLOYEE_IDS, entity.getEffectiveEmployeeIds());
        scopeApplication.put(MenuTemplateEntityProperty.SCOPE_LIST, entity.getScopeList());

        if (coverRange) {
            scopeApplication.entrySet().removeIf(entry -> CollectionUtils.isEmpty(entry.getValue()));   // addAll 限制不能插入空List
            scopeApplication.forEach((key, value) -> {
                update.addAll(key, value, false);   // add_to_set
            });
        } else {
            scopeApplication.forEach(update::set);
        }
        // createIfMissing: tue, 如果不存在符合query的, 就创建一个
        return datastore.findAndModify(query, update, true, true);
    }

    public MenuTemplateEntity update(int enterpriseId, String id, Integer employeeId, String name, List<Integer> employeeIds, List<Integer> departmentIds, List<String> roleIds, List<String> userGroupIds, List<Integer> effectiveEmployeeIds, List<MenuEntity> menus, int defaultMenuOrder, Long time, boolean goNewAppCustomer, List<String> scopeList, int priorityLevel) {
        roleIds = Objects.isNull(roleIds) ? new ArrayList<>() : roleIds;

        Query<MenuTemplateEntity> query = createQuery(enterpriseId, id);
        UpdateOperations<MenuTemplateEntity> update = createUpdate();
        if (goNewAppCustomer) {
            update.set(MenuTemplateEntityProperty.SCOPE_LIST, scopeList);
            update.set(MenuTemplateEntityProperty.PRIORITY, priorityLevel);
        } else {
            update = createUpdate(employeeId, ImmutableMap.of(
                    MenuTemplateEntityProperty.RANGE + "." + AdaptedRangeEntityProperty.EMPLOYEE_IDS, employeeIds,
                    MenuTemplateEntityProperty.RANGE + "." + AdaptedRangeEntityProperty.DEPARTMENT_IDS, departmentIds,
                    MenuTemplateEntityProperty.RANGE + "." + AdaptedRangeEntityProperty.ROLE_IDS, roleIds,
                    MenuTemplateEntityProperty.EFFECTIVE_EMPLOYEE_IDS, effectiveEmployeeIds,
                    MenuTemplateEntityProperty.DEFAULT_MENU_INDEX, defaultMenuOrder
            ), time);
            update.set(MenuTemplateEntityProperty.RANGE + "." + AdaptedRangeEntityProperty.USER_GROUP_IDS, userGroupIds);
        }
        update.set(MenuTemplateEntityProperty.NAME, name);
        update.set(MenuTemplateEntityProperty.MENUS, menus);
        update.set(MenuTemplateEntityProperty.UPDATE_TIME, time);

        //新增用户组

        return datastore.findAndModify(query, update, true);
    }

    public MenuTemplateEntity getByEmployeeId(int enterpriseId, Integer employeeId, int platform) {
        Query<MenuTemplateEntity> query = createQuery(enterpriseId);
        query.field(MenuTemplateEntityProperty.EFFECTIVE_EMPLOYEE_IDS).equal(employeeId);
        if (platform == 0) {
            query.or(query.criteria(MenuTemplateEntityProperty.PLATFORM).doesNotExist(),
                    query.criteria(MenuTemplateEntityProperty.PLATFORM).equal(PlatformType.F_XIAOKE));
        } else {
            query.field(MenuTemplateEntityProperty.PLATFORM).equal(platform);
        }
        return query.get();
    }

    public List<MenuTemplateEntity> getByEmployeeId(int enterpriseId, List<Integer> effectiveEmployeeIds) {
        Query<MenuTemplateEntity> query = createQuery(enterpriseId);
        query.field(MenuTemplateEntityProperty.EFFECTIVE_EMPLOYEE_IDS).in(effectiveEmployeeIds);
        return query.asList();
    }

    public List<MenuTemplateEntity> getByEmployeeId(int enterpriseId, List<Integer> effectiveEmployeeIds, int platform) {
        Query<MenuTemplateEntity> query = createQuery(enterpriseId);
        query.field(MenuTemplateEntityProperty.EFFECTIVE_EMPLOYEE_IDS).in(effectiveEmployeeIds);
        if (platform == 0) {
            query.or(query.criteria(MenuTemplateEntityProperty.PLATFORM).doesNotExist(),
                    query.criteria(MenuTemplateEntityProperty.PLATFORM).equal(PlatformType.F_XIAOKE));
        } else {
            query.field(MenuTemplateEntityProperty.PLATFORM).equal(platform);
        }
        return query.asList();
    }

    public List<MenuTemplateEntity> getByEmployeeIdWithoutId(int enterpriseId, List<Integer> employeeIds, CheckDuplicateRange.Arg arg) {
        Query<MenuTemplateEntity> query = createQuery(enterpriseId);
        if (Objects.nonNull(arg.getId())) {
            query.field(MenuTemplateEntityProperty.MENU_TEMPLATE_ID).notEqual(arg.getId());
        }
        query.field(MenuTemplateEntityProperty.EFFECTIVE_EMPLOYEE_IDS).in(employeeIds);
        if (arg.getPlatform() == 0) {
            query.or(query.criteria(MenuTemplateEntityProperty.PLATFORM).doesNotExist(),
                    query.criteria(MenuTemplateEntityProperty.PLATFORM).equal(PlatformType.F_XIAOKE));
        } else {
            query.field(MenuTemplateEntityProperty.PLATFORM).equal(arg.getPlatform());
        }
        return query.asList();
    }

    public List<MenuTemplateEntity> getByPageTemplateId(int enterpriseId, String pageTemplateId) {
        return createQuery(enterpriseId, ImmutableMap.of(
                MenuTemplateEntityProperty.MENUS + "." + MenuEntityProperty.TYPE, MenuType.PAGE_TEMPLATE,
                MenuTemplateEntityProperty.MENUS + "." + MenuEntityProperty.VALUE, pageTemplateId
        )).asList();
    }

    public List<MenuTemplateEntity> getByPageTemplateId(int enterpriseId, List<String> pageTemplateIds) {
        Query<MenuTemplateEntity> query = createQuery(enterpriseId, ImmutableMap.of(MenuTemplateEntityProperty.MENUS + "." + MenuEntityProperty.TYPE, MenuType.PAGE_TEMPLATE));
        query.field(MenuTemplateEntityProperty.MENUS + "." + MenuEntityProperty.VALUE).in(pageTemplateIds);
        return query.asList();
    }

    public List<MenuTemplateEntity> getAllMenuTemplate(int enterpriseId) {
        //todo  需要将 是否走新版App自定义参数同步过来是否这个参数同步过来
        return getAllMenuTemplate(enterpriseId, null, null, null, false);
    }

    public List<MenuTemplateEntity> getAllMenuTemplate(int enterpriseId, List<Integer> employeeIds, Integer offset, Integer size, boolean limit) {
        Query<MenuTemplateEntity> query = createQuery(enterpriseId);
        if (CollectionUtils.isNotEmpty(employeeIds)) {
            query.field(MenuTemplateEntityProperty.EFFECTIVE_EMPLOYEE_IDS).in(employeeIds);
        }
        //只显示预置模板
        if (limit) {
            query.field(MenuTemplateEntityProperty.SOURCE_TYPE).exists();
        }

        query.or(
                query.criteria(MenuTemplateEntityProperty.SCOPE_LIST).doesNotExist(),
                query.criteria(MenuTemplateEntityProperty.SCOPE_LIST).equal(null),
                query.criteria(MenuTemplateEntityProperty.SOURCE_TYPE).exists()
        );
        if (Objects.nonNull(offset)) {
            query.offset(offset);
        }
        if (Objects.nonNull(size)) {
            query.limit(size);
        }
        query.order("-_id");
        return query.asList();
    }


    public List<MenuTemplateEntity> getAllMenuTemplateByTenantId(int tenantId) {
        Query<MenuTemplateEntity> query = createQuery(tenantId);
        return query.asList();
    }

    public List<MenuTemplateEntity> getAllMenuTemplateByScpeList(int enterpriseId, Integer offset, Integer size, boolean limit, List<String> scopeList) {
        Query<MenuTemplateEntity> query = createQuery(enterpriseId);
        if (CollectionUtils.isNotEmpty(scopeList)) {
            query.field(MenuTemplateEntityProperty.SCOPE_LIST).hasAnyOf(scopeList);
        }
        query.field(MenuTemplateEntityProperty.SCOPE_LIST).exists();

        //只显示预置模板
        if (limit) {
            query.field(MenuTemplateEntityProperty.SOURCE_TYPE).exists();
        }

        if (Objects.nonNull(offset)) {
            query.offset(offset);
        }
        if (Objects.nonNull(size)) {
            query.limit(size);
        }
        query.order("-_id");
        return query.asList();
    }


    public long countAllMenuTemplate(int enterpriseId, List<Integer> employeeIds) {
        Query<MenuTemplateEntity> query = createQuery(enterpriseId);
        if (CollectionUtils.isNotEmpty(employeeIds)) {
            query.field(MenuTemplateEntityProperty.EFFECTIVE_EMPLOYEE_IDS).in(employeeIds);
        }
        return query.countAll();
    }

    public MenuTemplateEntity getMenuTemplate(int enterpriseId, String id) {
        return createQuery(enterpriseId, id).get();
    }

    public void updateConflictEmployeeId(int enterpriseId, Integer employeeId, String id, List<Integer> conflictEmployeeIds, Long time) {
        Query<MenuTemplateEntity> query = createQuery(enterpriseId, id);

        UpdateOperations<MenuTemplateEntity> update = createUpdate(employeeId, time);
        update.removeAll(MenuTemplateEntityProperty.EFFECTIVE_EMPLOYEE_IDS, conflictEmployeeIds);
        update.addAll(MenuTemplateEntityProperty.EXCLUDE_EMPLOYEE_IDS, conflictEmployeeIds, false);

        datastore.findAndModify(query, update);
    }

    public MenuTemplateEntity getMenuTemplateByName(Integer enterpriseId, String name, boolean goNewAppCustomer) {
        Query<MenuTemplateEntity> query = createQuery(enterpriseId);
        query.field(MenuTemplateEntityProperty.NAME).equal(name);
        if (goNewAppCustomer) {
            query.field(MenuTemplateEntityProperty.SCOPE_LIST).exists();
        } else {
            query.field(MenuTemplateEntityProperty.SCOPE_LIST).doesNotExist();
        }
        return query.get();
    }

    public MenuTemplateEntity getMenuTemplateByEmployeeId(Integer enterpriseId, Integer currentEmployeeId, int platform) {
        Query<MenuTemplateEntity> query = createQuery(enterpriseId);
        query.field(MenuTemplateEntityProperty.EFFECTIVE_EMPLOYEE_IDS).equal(currentEmployeeId);
        if (platform == 0) {
            query.or(query.criteria(MenuTemplateEntityProperty.PLATFORM).doesNotExist(),
                    query.criteria(MenuTemplateEntityProperty.PLATFORM).equal(PlatformType.F_XIAOKE));
        } else {
            query.field(MenuTemplateEntityProperty.PLATFORM).equal(platform);
        }

        return query.get();
    }

    public List<MenuTemplateEntity> getMenuTemplateByScopeList(Integer enterpriseId, int platform, List<String> scopeList) {
        Query<MenuTemplateEntity> query = createQuery(enterpriseId);
        query.field(MenuTemplateEntityProperty.SCOPE_LIST).in(scopeList);
        if (platform == 0) {
            query.or(query.criteria(MenuTemplateEntityProperty.PLATFORM).doesNotExist(),
                    query.criteria(MenuTemplateEntityProperty.PLATFORM).equal(PlatformType.F_XIAOKE));
        } else {
            query.field(MenuTemplateEntityProperty.PLATFORM).equal(platform);
        }
        //排序
        return query.asList();
    }

    public void addEmployeeIdByCreate(Integer ei, Integer employeeId, String id) {
        Query<MenuTemplateEntity> query = createQuery(ei, id);
        UpdateOperations<MenuTemplateEntity> update = createUpdate();
        update.add(MenuTemplateEntityProperty.EFFECTIVE_EMPLOYEE_IDS, employeeId);

        datastore.findAndModify(query, update);
    }


    public List<MenuTemplateEntity> getByDepartmentId(Integer ei, List<Integer> departmentIds, int platform) {
        Query<MenuTemplateEntity> query = createQuery(ei);
        query.field(MenuTemplateEntityProperty.RANGE + "." + AdaptedRangeEntityProperty.DEPARTMENT_IDS).in(departmentIds);
        if (platform == 0) {
            query.or(query.criteria(MenuTemplateEntityProperty.PLATFORM).doesNotExist(),
                    query.criteria(MenuTemplateEntityProperty.PLATFORM).equal(PlatformType.F_XIAOKE));
        } else {
            query.field(MenuTemplateEntityProperty.PLATFORM).equal(platform);
        }
        return query.asList();
    }

    public List<MenuTemplateEntity> getByRoleId(Integer ei, List<String> roleCode, int platform) {
        Query<MenuTemplateEntity> query = createQuery(ei);
        query.field(MenuTemplateEntityProperty.RANGE + "." + AdaptedRangeEntityProperty.ROLE_IDS).in(roleCode);
        if (platform == 0) {
            query.or(query.criteria(MenuTemplateEntityProperty.PLATFORM).doesNotExist(),
                    query.criteria(MenuTemplateEntityProperty.PLATFORM).equal(PlatformType.F_XIAOKE));
        } else {
            query.field(MenuTemplateEntityProperty.PLATFORM).equal(platform);
        }
        return query.asList();
    }


    public List<MenuTemplateEntity> getByUserGroup(Integer ei, List<String> userGroups, int platform) {
        Query<MenuTemplateEntity> query = createQuery(ei);
        query.field(MenuTemplateEntityProperty.RANGE + "." + AdaptedRangeEntityProperty.USER_GROUP_IDS).in(userGroups);
        if (platform == 0) {
            query.or(query.criteria(MenuTemplateEntityProperty.PLATFORM).doesNotExist(),
                    query.criteria(MenuTemplateEntityProperty.PLATFORM).equal(PlatformType.F_XIAOKE));
        } else {
            query.field(MenuTemplateEntityProperty.PLATFORM).equal(platform);
        }
        return query.asList();
    }


    public List<MenuTemplateEntity> getByRole(Integer ei, String roleId) {
        Query<MenuTemplateEntity> query = createQuery(ei);
        query.field(MenuTemplateEntityProperty.RANGE + "." + AdaptedRangeEntityProperty.ROLE_IDS).equal(roleId);
        return query.asList();
    }

    public List<MenuTemplateEntity> batchGetByRoleByIds(Integer ei, Set<String> roleIdList) {
        Query<MenuTemplateEntity> query = createQuery(ei);
        query.field(MenuTemplateEntityProperty.RANGE + "." + AdaptedRangeEntityProperty.ROLE_IDS).hasAnyOf(roleIdList);
        query.order("-" + MenuTemplateEntityProperty.CREATE_TIME);
        return query.asList();
    }

    public List<MenuTemplateEntity> batchGetByRoleByUserGroup(Integer ei, String userGroupId, List<Integer> userIds) {
        Query<MenuTemplateEntity> query = createQuery(ei);
        query.field(MenuTemplateEntityProperty.RANGE + "." + AdaptedRangeEntityProperty.USER_GROUP_IDS).equal(userGroupId);
        query.field(MenuTemplateEntityProperty.EFFECTIVE_EMPLOYEE_IDS).in(userIds);
        return query.asList();
    }

    public List<MenuTemplateEntity> getByRoleByUserGroup(Integer ei, String userGroupId) {
        Query<MenuTemplateEntity> query = createQuery(ei);
        query.field(MenuTemplateEntityProperty.RANGE + "." + AdaptedRangeEntityProperty.USER_GROUP_IDS).equal(userGroupId);
        query.order("-" + MenuTemplateEntityProperty.CREATE_TIME);
        return query.asList();
    }

    public void addEmployeeIds(int ei, String menuTemplateId, List<Integer> employeeIds) {
        Query<MenuTemplateEntity> query = createQuery(ei, menuTemplateId);
        UpdateOperations<MenuTemplateEntity> update = createUpdate();
        update.addAll(MenuTemplateEntityProperty.EFFECTIVE_EMPLOYEE_IDS, employeeIds, false);
        datastore.findAndModify(query, update);
    }


    public void removeEmployeeIds(int ei, String menuTemplateId, List<Integer> employeeIds) {
        Query<MenuTemplateEntity> query = createQuery(ei, menuTemplateId);
        UpdateOperations<MenuTemplateEntity> update = createUpdate();
        update.removeAll(MenuTemplateEntityProperty.EFFECTIVE_EMPLOYEE_IDS, employeeIds);
        datastore.findAndModify(query, update);
    }


    public void removeEmployeeId(Integer ei, String menuTemplateId, Integer employeeId) {
        Query<MenuTemplateEntity> query = createQuery(ei, menuTemplateId);
        UpdateOperations<MenuTemplateEntity> update = createUpdate();
        update.removeAll(MenuTemplateEntityProperty.EFFECTIVE_EMPLOYEE_IDS, employeeId);

        datastore.findAndModify(query, update);
    }

    public List<MenuTemplateEntity> getByNotifyIds(int enterpriseId, Integer type, String menuItemId) {
        return createQuery(enterpriseId, ImmutableMap.of(
                MenuTemplateEntityProperty.MENUS + "." + MenuEntityProperty.TYPE, type,
                MenuTemplateEntityProperty.MENUS + "." + MenuEntityProperty.VALUE, menuItemId
        )).asList();
    }

    // ====== transfer
    public List<MenuTemplateEntity> batchGetMenuTemplate(List<String> ids) {
        Query<MenuTemplateEntity> query = createQuery();
        query.field(MenuTemplateEntityProperty.MENU_TEMPLATE_ID).in(ids);
        return query.asList();
    }

    // 预置模板
    public void removeMenuTemplateRange(int ei, List<Integer> employeeIds, List<Integer> departmentIds, List<String> roleIds) {
        roleIds = Objects.isNull(roleIds) ? new ArrayList<>() : roleIds;
        Query<MenuTemplateEntity> query = createQuery(ei);
        if (employeeIds.isEmpty() && departmentIds.isEmpty()) {
            return;
        }
        if (!employeeIds.isEmpty() && !departmentIds.isEmpty()) {
            query.or(
                    query.criteria(MenuTemplateEntityProperty.RANGE + "." + AdaptedRangeEntityProperty.DEPARTMENT_IDS).in(departmentIds),
                    query.criteria(MenuTemplateEntityProperty.RANGE + "." + AdaptedRangeEntityProperty.ROLE_IDS).in(roleIds),
                    query.criteria(MenuTemplateEntityProperty.EFFECTIVE_EMPLOYEE_IDS).in(employeeIds)
            );
        }
        if (!employeeIds.isEmpty() && departmentIds.isEmpty()) {
            query.field(MenuTemplateEntityProperty.EFFECTIVE_EMPLOYEE_IDS).in(employeeIds);
        }

        if (employeeIds.isEmpty() && !departmentIds.isEmpty()) {
            query.field(MenuTemplateEntityProperty.RANGE + "." + AdaptedRangeEntityProperty.DEPARTMENT_IDS).in(departmentIds);
        }

        UpdateOperations<MenuTemplateEntity> update = createUpdate();
        if (!employeeIds.isEmpty()) {
            update.removeAll(MenuTemplateEntityProperty.RANGE + "." + AdaptedRangeEntityProperty.EMPLOYEE_IDS, employeeIds);
            update.removeAll(MenuTemplateEntityProperty.EFFECTIVE_EMPLOYEE_IDS, employeeIds);
        }
        if (!departmentIds.isEmpty()) {
            update.removeAll(MenuTemplateEntityProperty.RANGE + "." + AdaptedRangeEntityProperty.DEPARTMENT_IDS, departmentIds);
        }
        if (!roleIds.isEmpty()) {
            update.removeAll(MenuTemplateEntityProperty.RANGE + "." + AdaptedRangeEntityProperty.ROLE_IDS, roleIds);
        }

        datastore.update(query, update);
    }

    public void removeMenuTemplateRangeByAllCompany(int ei) {
        Query<MenuTemplateEntity> query = createQuery(ei);

        UpdateOperations<MenuTemplateEntity> update = createUpdate();
        update.set(MenuTemplateEntityProperty.STATUS, MenuTemplateStatus.DELETE);

        datastore.update(query, update);
    }

    public List<MenuTemplateEntity> getByRoleOrDepartmentId(Integer ei, String roleId, Integer departmentId, int platform) {
        boolean roleBlank = StringUtils.isBlank(roleId);
        boolean departmentNull = Objects.isNull(departmentId);
        if (roleBlank && departmentNull) {
            return new ArrayList<>();
        }

        Query<MenuTemplateEntity> query = createQuery(ei);
        if (!roleBlank && !departmentNull) {
            query.or(
                    query.criteria(MenuTemplateEntityProperty.RANGE + "." + AdaptedRangeEntityProperty.ROLE_IDS).equal(roleId),
                    query.criteria(MenuTemplateEntityProperty.RANGE + "." + AdaptedRangeEntityProperty.DEPARTMENT_IDS).equal(departmentId)
            );
        } else if (!roleBlank) {
            query.field(MenuTemplateEntityProperty.RANGE + "." + AdaptedRangeEntityProperty.ROLE_IDS).equal(roleId);
        } else {
            query.field(MenuTemplateEntityProperty.RANGE + "." + AdaptedRangeEntityProperty.DEPARTMENT_IDS).equal(departmentId);
        }
        if (platform == 0) {
            query.or(query.criteria(MenuTemplateEntityProperty.PLATFORM).doesNotExist(),
                    query.criteria(MenuTemplateEntityProperty.PLATFORM).equal(PlatformType.F_XIAOKE));
        } else {
            query.field(MenuTemplateEntityProperty.PLATFORM).equal(platform);
        }

        return query.asList();
    }

    public List<MenuTemplateEntity> getBySettingEmployee(Integer enterpriseId, Integer employeeId, int platform) {
        Query<MenuTemplateEntity> query = createQuery(enterpriseId);
        query.field(MenuTemplateEntityProperty.RANGE + "." + AdaptedRangeEntityProperty.EMPLOYEE_IDS).equal(employeeId);
        if (platform == 0) {
            query.or(query.criteria(MenuTemplateEntityProperty.PLATFORM).doesNotExist(),
                    query.criteria(MenuTemplateEntityProperty.PLATFORM).equal(PlatformType.F_XIAOKE));
        } else {
            query.field(MenuTemplateEntityProperty.PLATFORM).equal(platform);
        }
        return query.asList();
    }

    public void removeEmployeeIdByStop(Integer enterpriseId, Integer employeeId) {
        Query<MenuTemplateEntity> query = createQuery(enterpriseId);
        query.or(
                query.criteria(MenuTemplateEntityProperty.RANGE + "." + AdaptedRangeEntityProperty.EMPLOYEE_IDS).equal(employeeId),
                query.criteria(MenuTemplateEntityProperty.EFFECTIVE_EMPLOYEE_IDS).equal(employeeId)
        );

        UpdateOperations<MenuTemplateEntity> update = createUpdate();
        update.removeAll(MenuTemplateEntityProperty.EFFECTIVE_EMPLOYEE_IDS, employeeId);
        update.removeAll(MenuTemplateEntityProperty.RANGE + "." + AdaptedRangeEntityProperty.EMPLOYEE_IDS, employeeId);

        datastore.update(query, update);
    }

    public void removeDepartmentIdByStop(int enterpriseId, int departmentId) {
        Query<MenuTemplateEntity> query = createQuery(enterpriseId);
        query.field(MenuTemplateEntityProperty.RANGE + "." + AdaptedRangeEntityProperty.DEPARTMENT_IDS).equal(departmentId);

        UpdateOperations<MenuTemplateEntity> update = createUpdate();
        update.removeAll(MenuTemplateEntityProperty.RANGE + "." + AdaptedRangeEntityProperty.DEPARTMENT_IDS, departmentId);

        datastore.update(query, update);
    }

    public List<MenuTemplateEntity> getBySourceTypes(Integer enterpriseId, List<String> sourceTypes) {
        Query<MenuTemplateEntity> query = createQuery(enterpriseId);
        if (CollectionUtils.isNotEmpty(sourceTypes)) {
            query.field(MenuTemplateEntityProperty.SOURCE_TYPE).in(sourceTypes);
        }
        return query.asList();
    }

    public boolean hasPartialOperationAuth(Integer enterpriseId) {
        Query<MenuTemplateEntity> query = createQuery(enterpriseId);
        query.field(MenuTemplateEntityProperty.OPERATION_AUTH)
                .in(Lists.newArrayList(TemplateOperationAuth.PARTIAL, TemplateOperationAuth.ALL));
        return Objects.nonNull(query.get());
    }

    public List<MenuTemplateEntity> queryMenuTemplateEntity(Integer enterpriseId) {
        Query<MenuTemplateEntity> query = createQuery(enterpriseId);
        return query.asList();
    }

    public MenuTemplateEntity setRange(int enterpriseId, String id, Integer employeeId, List<Integer> employeeIds, List<Integer> departmentIds, List<String> roleIds, List<Integer> effectiveEmployeeIds, Long time) {
        roleIds = Objects.isNull(roleIds) ? new ArrayList<>() : roleIds;

        Query<MenuTemplateEntity> query = createQuery(enterpriseId, id);
        UpdateOperations<MenuTemplateEntity> update = createUpdate(employeeId, ImmutableMap.of(
                MenuTemplateEntityProperty.RANGE + "." + AdaptedRangeEntityProperty.EMPLOYEE_IDS, employeeIds,
                MenuTemplateEntityProperty.RANGE + "." + AdaptedRangeEntityProperty.DEPARTMENT_IDS, departmentIds,
                MenuTemplateEntityProperty.RANGE + "." + AdaptedRangeEntityProperty.ROLE_IDS, roleIds,
                MenuTemplateEntityProperty.EFFECTIVE_EMPLOYEE_IDS, effectiveEmployeeIds
        ), time);

        return datastore.findAndModify(query, update, true);
    }

    public List<MenuTemplateEntity> getAllBySourceTypeNotEmpty() {
        Query<MenuTemplateEntity> query = createQuery();
        query.field(MenuTemplateEntityProperty.SOURCE_TYPE).exists();

        return query.asList();
    }

    public List<MenuTemplateEntity> getAllMenuTemplate() {
        Query<MenuTemplateEntity> query = createQuery();
        query.field(MenuTemplateEntityProperty.STATUS).equal(MenuTemplateStatus.NORMAL);
        return query.asList();
    }

    public MenuTemplateEntity getBySourceId(Integer enterpriseId, String templateId) {
        Query<MenuTemplateEntity> query = createQuery(enterpriseId);
        query.field(MenuTemplateEntityProperty.SOURCE_ID).equal(templateId);
        return query.get();
    }


    public List<MenuTemplateEntity> getAllMenuTemplateByTenantIds(boolean grayTenantId, List<Integer> tenantIdList, int count, int size) {
        Query<MenuTemplateEntity> query = createQuery();
        query.field(MenuTemplateEntityProperty.STATUS).equal(MenuTemplateStatus.NORMAL);
        if (grayTenantId) {
            query.field(MenuTemplateEntityProperty.ENTERPRISE_ID).in(tenantIdList);
        } else {
            query.field(MenuTemplateEntityProperty.ENTERPRISE_ID).hasNoneOf(tenantIdList);
        }
        query.order("_id");
        query.limit(size);
        query.offset((count) * size);
        return query.asList();
    }

    public void updateMenuTemplateMenu(List<MenuTemplate> templates) {
        for (MenuTemplate menuTemplate : templates) {
            Query<MenuTemplateEntity> query = datastore.createQuery(MenuTemplateEntity.class);
            query.field(MenuTemplateEntityProperty.MENU_TEMPLATE_ID).equal(menuTemplate.getMenuTemplateId());
            UpdateOperations<MenuTemplateEntity> update = createUpdate();
            List<MenuEntity> menuEntities = MenuEntity.convert2Entity(menuTemplate.getMenus());
            update.set(MenuTemplateEntityProperty.MENUS, menuEntities);
            MenuTemplateEntity menuTemplateEntity = datastore.findAndModify(query, update, false, false);
            logger.info("update menuTemplate ei {}, menuTemplateId {}, menuEntity{}", menuTemplateEntity.getEnterpriseId(), menuTemplateEntity.getMenuTemplateId(), menuTemplateEntity.getMenus());
        }
    }
}
