package com.fxiaoke.biz.dao;

/**
 * Description
 * Created by shang<PERSON>peng on 16/8/9.
 */
public interface SplashConfigField {
    String versionNumber = "VN";//版本号

    String disappearType = "DT";//消失方式,0:点击消失,>0:在指定秒数后自动消失

    String showType  = "ST";//显示方式,0:不显示,1:启动时显示;2:进程启动和home后进入都显示

    // android：  key为1，480×800；key为2，640×960；key为3，800×1280；
    //图片地址集合 iphone:key为1，320×480；key为2，640×960；key为3，640×1360；
    String picUrls = "PUS"; //照片的url

    String intervalTime = "IT";//间隔时间,单位消失,0为不间隔

    String startDate = "SD";//有效开始时间

    String endDate = "ED";//有效结束时间

    String startVersion = "SV";// 匹配的客户端开始版本号

    String endVersion = "EV";// 匹配的客户端结束版本号

    String isTest = "isTest";// 是否是给测试客户端的配置

    String osType = "OS";// 系统类型

    String deleted = "DEL";
    String createTime = "CT";

    String enterpriseId = "EI";

    String userId = "UI";
}
