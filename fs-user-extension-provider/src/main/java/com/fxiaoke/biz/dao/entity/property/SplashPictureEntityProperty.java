package com.fxiaoke.biz.dao.entity.property;

public interface SplashPictureEntityProperty {
    /**
     * 企业id
     */
    String ENTERPRISE_ID = "EI";
    /**
     * 闪屏id
     */
    String SPLASH_PICTURE_ID = "SPId";
    /**
     * 显示时长 毫秒
     */
    String DURATION = "showTime";
    /**
     * 点击跳转url
     */
    String ACTION = "action";
    /**
     * 图片url
     */
    String IMAGE_URL = "url";
    /**
     * 图片info json串
     */
    String PICTURE_INFO = "PI";
    /**
     * 状态 1=正常、2=删除
     */
    String STATUS = "status";
    /**
     * 最后修改人员id
     */
    String UPDATE_EMPLOYEE_ID = "LMEmpId";
    /**
     * 更新时间
     */
    String UPDATE_TIME = "UT";
}
