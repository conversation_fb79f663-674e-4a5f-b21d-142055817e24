package com.fxiaoke.biz.dao;

import com.facishare.qixin.datastore.BaseDaoImpl;
import com.fxiaoke.biz.dao.entity.PersonCommonMenuEntity;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/2/28 3:00 PM
 */
public class UserMenuDaoImpl extends BaseDaoImpl<PersonCommonMenuEntity> {


    public void savePersonCommonMenus(PersonCommonMenuEntity personCommonMenuEntity) {
        int enterpriseId = personCommonMenuEntity.getEnterpriseId();
        Query<PersonCommonMenuEntity> query = createQuery(enterpriseId);
        String userId = personCommonMenuEntity.getUserId();
        query.field("userId").equal(userId);
        String pageTemplateId = personCommonMenuEntity.getPageTemplateId();
        query.field("pageTemplateId").equal(pageTemplateId);
        String menuGroupApiName = personCommonMenuEntity.getMenuGroupApiName();
        query.field("menuGroupApiName").equal(menuGroupApiName);

        UpdateOperations<PersonCommonMenuEntity> updateOperations = createUpdateOperations(enterpriseId);
        updateOperations.setOnInsert("userId", userId);
        updateOperations.setOnInsert("pageTemplateId", pageTemplateId);
        updateOperations.setOnInsert("menuGroupApiName", menuGroupApiName);

        updateOperations.set("menuItemList", personCommonMenuEntity.getMenuItemList());
        updateOperations.set("updateTime", personCommonMenuEntity.getUpdateTime());


        datastore.findAndModify(query, updateOperations, false, true);
    }

    public PersonCommonMenuEntity queryCommonMenus(int enterpriseId,
                                                   String userId,
                                                   String pageTemplateId,
                                                   String groupApiName) {
        Query<PersonCommonMenuEntity> query = createQuery(enterpriseId);
        query.field("userId").equal(userId);
        query.field("pageTemplateId").equal(pageTemplateId);
        query.field("menuGroupApiName").equal(groupApiName);

        return query.get();
    }

    public List<PersonCommonMenuEntity> queryPersonCommonMenuEntities(int enterpriseId,
                                                                      String userId,
                                                                      String pageTemplateId) {
        Query<PersonCommonMenuEntity> query = createQuery(enterpriseId);
        query.field("userId").equal(userId);
        query.field("pageTemplateId").equal(pageTemplateId);

        return query.asList();
    }

    @Override
    protected Class<PersonCommonMenuEntity> getEntityClazz() {
        return PersonCommonMenuEntity.class;
    }
}
