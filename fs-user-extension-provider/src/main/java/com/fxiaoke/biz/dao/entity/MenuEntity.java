package com.fxiaoke.biz.dao.entity;

import com.fxiaoke.api.model.Menu;
import com.fxiaoke.api.model.type.MenuType;
import com.fxiaoke.biz.dao.entity.property.MenuEntityProperty;
import com.fxiaoke.biz.profile.ConfigCoreProviderService;
import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.mongodb.morphia.annotations.Embedded;
import org.mongodb.morphia.annotations.Property;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 菜单数据
 *
 * <AUTHOR>
 */
@Embedded
@Data
public class MenuEntity {
    /**
     * 菜单名称
     */
    @Property(MenuEntityProperty.NAME)
    private String name;
    /**
     * 菜单类型
     *
     * @see MenuType
     * 1.应用 2.页面模板 3.自定义url
     */
    @Property(MenuEntityProperty.TYPE)
    private Integer type;
    /**
     * 与type联动,产生不同的值
     * menuType = 1, 应用id
     * menuType = 2, 页面模板id
     * menuType = 3, url
     */
    @Property(MenuEntityProperty.VALUE)
    private String value;

    @Property(MenuEntityProperty.UPSTREAM_EA)
    private String upStreamEa;

    @Property(MenuEntityProperty.ICON_INDEX)
    private Integer iconIndex;
    @Property(MenuEntityProperty.NEW_NAME)
    private String newName;
    @Property(MenuEntityProperty.CUSTOMIZE_ICON)
    private String customizeIcon;
    @Property(MenuEntityProperty.CUSTOMIZE_SELECT_ICON)
    private String customIzeSelectIcon;
    @Property(MenuEntityProperty.IS_HOME)
    private Boolean isHome;
    @Property(MenuEntityProperty.ORDER)
    private Integer order;
    @Property(MenuEntityProperty.USER_UPLOAD)
    private boolean userUpload;

    private String recordTypeApiName;

    public static List<MenuEntity> convert2Entity(Collection<Menu> menus) {
        if (CollectionUtils.isEmpty(menus)) {
            return new ArrayList<>();
        }
        return menus.stream().map(MenuEntity::convert2Entity).collect(Collectors.toList());
    }

    public static MenuEntity convert2Entity(Menu menu) {
        MenuEntity menuEntity = new MenuEntity();
        menuEntity.setName(menu.getName());
        menuEntity.setNewName(menu.getNewName());
        menuEntity.setType(menu.getMenuType());
        menuEntity.setValue(menu.getValue());
        menuEntity.setUpStreamEa(menu.getUpStreamEa());
        menuEntity.setCustomizeIcon(menu.getCustomizeIcon());
        menuEntity.setCustomIzeSelectIcon(menu.getCustomizeSelectIcon());
        menuEntity.setOrder(menu.getOrder());
        menuEntity.setIsHome(menu.getIsHome());
        menuEntity.setRecordTypeApiName(menu.getRecordTypeApiName());
        menuEntity.setUserUpload(menu.isUserUpload());
        return menuEntity;
    }

    public static List<Menu> convert2Dto(List<MenuEntity> menus) {
        if (CollectionUtils.isEmpty(menus)) {
            return new ArrayList<>();
        }
        return menus.stream().map(MenuEntity::convert2Dto).collect(Collectors.toList());
    }

    public static List<Menu> convert2Dto(List<MenuEntity> menus, int defaultMenuIndex) {
        if (CollectionUtils.isEmpty(menus)) {
            return new ArrayList<>();
        }
        List<Menu> menuList = Lists.newArrayList();
        for (int i = 0; i < menus.size(); i++) {
            MenuEntity menuEntity = menus.get(i);
            Menu menu = convert2Dto(menuEntity);
            if (Objects.isNull(menu.getIsHome())) {
                if (defaultMenuIndex == i) {
                    menu.setIsHome(true);
                } else {
                    menu.setIsHome(false);
                }
            }
            if (menu.getOrder() == null){
                menu.setOrder(i);
            }
            menuList.add(menu);
        }
        return menuList;
    }

    public static Menu convert2Dto(MenuEntity entity) {
        if (Objects.isNull(entity)) {
            return null;
        }

        if (Objects.equals(entity.getType(), MenuType.APP)) {
            Menu menu = ConfigCoreProviderService.getAppMenu(entity.getValue());
            if (Objects.isNull(menu)) {
                return covertNewMenu(entity);
            }
            menu.setName(entity.getName());
            menu.setNewName(entity.getNewName());
            menu.setCustomizeIcon(entity.getCustomizeIcon());
            menu.setCustomizeSelectIcon(entity.getCustomIzeSelectIcon());
            menu.setOrder(entity.order);
            menu.setUserUpload(entity.userUpload);
            return menu;
        }

        Menu menu = covertNewMenu(entity);
        return menu;
    }

    @NotNull
    private static Menu covertNewMenu(MenuEntity entity) {
        Menu menu = new Menu();
        menu.setName(entity.name);
        menu.setMenuType(entity.type);
        menu.setValue(entity.value);
        menu.setUpStreamEa(entity.getUpStreamEa());
        menu.setIconIndex(entity.getIconIndex());
        menu.setIsHome(entity.isHome);
        menu.setOrder(entity.order);
        menu.setNewName(entity.getNewName());
        menu.setCustomizeIcon(entity.getCustomizeIcon());
        menu.setCustomizeSelectIcon(entity.getCustomIzeSelectIcon());
        menu.setRecordTypeApiName(entity.recordTypeApiName);
        menu.setUserUpload(entity.userUpload);
        return menu;
    }

}
