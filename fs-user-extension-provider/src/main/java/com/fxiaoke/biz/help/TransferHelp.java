//package com.fxiaoke.biz.help;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import com.fxiaoke.api.model.BizDescription;
//import com.fxiaoke.biz.dao.AppConfigField;
//import com.fxiaoke.biz.dao.AppConfigPO;
//import com.fxiaoke.biz.dao.MenuTemplateDao;
//import com.fxiaoke.biz.dao.entity.MenuTemplateEntity;
//import com.fxiaoke.biz.profile.ConfigCoreService;
//import org.mongodb.morphia.Datastore;
//import org.mongodb.morphia.query.Query;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.util.List;
//import java.util.Map;
//import java.util.Objects;
//import java.util.function.Function;
//import java.util.stream.Collectors;
//
//@Component
//public class TransferHelp {
//
//    @Resource(name = "datastore")
//    private Datastore datastore;
//
//    @Autowired
//    private ConfigCoreService configCoreService;
//
//    @Autowired
//    private MenuTemplateDao menuTemplateDao;
//
//    Map<String, BizDescription> bizDescriptionActionMap;
//
//    public void aa() {
//        List<BizDescription> allBizDescription = configCoreService.getAllBizDescription(0);
//        //  2018/12/3 action指的是哪个?
//        bizDescriptionActionMap = allBizDescription.stream().collect(Collectors.toMap(b -> b.getAction().getUrl(), Function.identity()));
//    }
//
//    private List<AppConfigPO> getOldAppConfig() {
//        final Query<AppConfigPO> query = datastore.createQuery(AppConfigPO.class);
//        query.field(AppConfigField.deleted).equal(false);
//        return query.asList();
//    }
//
//    private String defaultApp = "{\"app\":{\"name\":\"纷享销客\"}";
//
//    private void convertOldAppSetting2Menu(AppConfigPO po, Map<Integer, String> changeAppMap) {
//        MenuTemplateEntity entity = new MenuTemplateEntity();
//
//        JSONObject jsonObject = JSON.parseObject(po.getSetting());
//        String app = jsonObject.getString("app");
//        if (!Objects.equals(defaultApp, app)) {
//            changeAppMap.put((int) po.getEnterpriseId(), app);
//        }
//
//        JSONObject style = jsonObject.getJSONObject("style");
//        JSONObject menu = jsonObject.getJSONObject("menu");
//        String template = menu.getString("template");
//        Integer defaultItemIndex = menu.getInteger("defaultItemIndex");
//        JSONArray items = jsonObject.getJSONArray("items");
//        for (int i = 0; i < items.size(); i++) {
//            JSONObject item = items.getJSONObject(i);
//            String action = item.getString("action");
//
//            bizDescriptionActionMap.put(,);
//        }
//    }
//
//
//    private void convertOldAppSetting2App() {
//
//    }
//
//
//}
