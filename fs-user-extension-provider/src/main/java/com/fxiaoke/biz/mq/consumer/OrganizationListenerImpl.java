package com.fxiaoke.biz.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.facishare.organization.adapter.api.model.biz.RunStatus;
import com.facishare.organization.api.event.OrganizationChangedListener;
import com.facishare.organization.api.event.organizationChangeEvent.DepartmentChangeEvent;
import com.facishare.organization.api.event.organizationChangeEvent.EmployeeChangeEvent;
import com.facishare.organization.api.event.organizationChangeEvent.TreeChangeEvent;
import com.facishare.organization.api.model.department.DepartmentDto;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.organization.api.model.type.DepartmentStatus;
import com.facishare.organization.api.model.type.EmployeeEntityStatus;
import com.facishare.webpage.customer.api.model.Scope;
import com.facishare.webpage.customer.api.model.arg.QueryScopeListRestArg;
import com.facishare.webpage.customer.api.service.TenantPageTempleRestService;
import com.fxiaoke.api.model.type.PlatformType;
import com.fxiaoke.biz.config.UiPaasConfig;
import com.fxiaoke.biz.dao.MenuTemplateDao;
import com.fxiaoke.biz.dao.entity.MenuTemplateEntity;
import com.fxiaoke.biz.mq.AppCustomizeMenuChangeNotify;
import com.fxiaoke.biz.mq.NaviAppChangeNotifyImpl;
import com.fxiaoke.biz.profile.ConfigCoreProviderService;
import com.fxiaoke.biz.remote.OrganizationService;
import com.fxiaoke.biz.remote.PaaSRoleService;
import com.fxiaoke.biz.remote.PollingService;
import com.fxiaoke.biz.remote.QiXinNotifyService;
import com.fxiaoke.biz.util.EmployeeId;
import com.github.autoconf.helper.ConfigHelper;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 员工/部门 变动需要修改对应的菜单模板
 * 员工修改
 * 创建:
 * 在适用范围部门中:
 * 只有一条菜单模板:直接加入该模板中
 * 有两条以上模板:发企信消息,不加入任何模板
 * 不在适用范围中:
 * 不加入任何模板
 * 修改主部门:
 * 检查当前员工是否有适用模板
 * 没有:
 * 检查适用模板数量(角色/部门)
 * 只有一条菜单模板:直接加入该模板中
 * 有两条以上模板:发企信消息,不加入任何模板
 * 有:检查适用范围:
 * 指定到当前员工:
 * 不修改
 * 指定到修改后的部门/角色:
 * 不修改
 * 其他:
 * 1.删除 自己在模板中 有效范围
 * 2.检查适用模板数量(角色/部门)
 * 只有一条菜单模板:直接加入该模板中
 * 有两条以上模板:发企信消息,不加入任何模板
 * 部门修改:
 * 停用/删除:
 * <p>
 * 部门架构树变更:
 */
@Slf4j
@Component
public class OrganizationListenerImpl extends OrganizationChangedListener {

    @Autowired
    private MenuTemplateDao menuTemplateDao;

    @Autowired
    private OrganizationService organizationService;

    @Autowired
    private NaviAppChangeNotifyImpl naviAppChangeNotify;

    @Autowired
    private PollingService pollingService;

    @Autowired
    private AppCustomizeMenuChangeNotify appCustomizeMenuChangeNotify;

    @Autowired
    private PaaSRoleService paaSRoleService;

    @Autowired
    private QiXinNotifyService qiXinNotifyService;
    @Autowired
    private ConfigCoreProviderService configCoreProviderService;
    @Autowired
    private UiPaasConfig uiPaasConfig;
    @Autowired
    private TenantPageTempleRestService tenantPageTempleRestService;




    @Override
    protected boolean isSkipConsumerWork() {
//        有group不消费mq
        return Objects.nonNull(System.getProperty("group"));
    }

    public OrganizationListenerImpl() {
        super("fs-user-ext-organization-change-mq");
    }

    @Override
    protected void onEmployeeChanged(EmployeeChangeEvent event) {
        int ei = event.getEnterpriseId();
        log.info("onEmployeeChanged,ei:{},body:{}", ei, JSON.toJSONString(event));
        //黑名单企业直接跳过
        if (ConfigCoreProviderService.isInEiBlacklistOnTreeChange(String.valueOf(ei))) {
            log.warn("onEmployeeChanged skip blacklist:{}", ei);
            return;
        }

        try {
            //通过context透传ei
            TraceContext.get()
                    .setTraceId(ConfigHelper.getProcessInfo().getName() + "/" + UUID.randomUUID().toString().replace("-", ""))
                    .setEi(String.valueOf(ei))
                    .setEa(event.getEnterpriseAccount());
            long startTime = System.currentTimeMillis();

            Integer employeeId;
            EmployeeDto newEmployeeDto = event.getNewEmployeeDto();
            EmployeeDto oldEmployeeDto = event.getOldEmployeeDto();
            if (newEmployeeDto == null && oldEmployeeDto == null) {
                return;
            }

            if (oldEmployeeDto != null) {
                employeeId = oldEmployeeDto.getEmployeeId();
            } else {
                employeeId = newEmployeeDto.getEmployeeId();
            }

            Integer oldMainDepartmentId = null;
            if (oldEmployeeDto != null) {
                oldMainDepartmentId = oldEmployeeDto.getMainDepartmentId();
            }

            Integer newMainDepartmentId = null;
            if (newEmployeeDto != null) {
                newMainDepartmentId = newEmployeeDto.getMainDepartmentId();
            }

            for (int platform : configCoreProviderService.getLicenseList()) {
                // 新建员工/删除
                if (newEmployeeDto == null || oldEmployeeDto == null) {
                    changeByEmployeeHasNull(ei, employeeId, newEmployeeDto, newMainDepartmentId, platform);
                } else if (!Objects.equals(oldMainDepartmentId, newMainDepartmentId)) {
                    // 变更部门（只关心主属部门）
                    changeByDepartmentChange(ei, employeeId, newMainDepartmentId, platform);
                } else if (!Objects.equals(newEmployeeDto.getStatus(), oldEmployeeDto.getStatus())) {
                    changeByStatusChange(ei, employeeId, newEmployeeDto, platform);
                }
            }

            log.info("onDepartmentChanged.success,ei:{},cost:{}", ei, System.currentTimeMillis() - startTime);
        } catch (Exception ex) {
            log.error("organization change error.", ex);
            throw ex;
        } finally {
            TraceContext.remove();
        }
    }

    private void changeByStatusChange(Integer ei, Integer employeeId, EmployeeDto newEmployeeDto, int platform) {
        if (Objects.equals(newEmployeeDto.getStatus(), EmployeeEntityStatus.STOP)) {
            MenuTemplateEntity entity = new MenuTemplateEntity();
            if (uiPaasConfig.checkNewAppCustomer(ei) && configCoreProviderService.useNewAppMenuSearchFlag) {
                QueryScopeListRestArg queryScopeListRestArg = new QueryScopeListRestArg();
                queryScopeListRestArg.setTenantId(ei);
                queryScopeListRestArg.setEmployeeId(employeeId);
                List<Scope> userScopeList = tenantPageTempleRestService.queryScopeList(String.valueOf(ei), queryScopeListRestArg).getScopeList();
                List<String> listScope = EmployeeId.getScopeListStringByScopeList(userScopeList);
                List<MenuTemplateEntity> menuTemplateEntitys = menuTemplateDao.getMenuTemplateByScopeList(ei, PlatformType.F_XIAOKE, listScope);
                if (CollectionUtils.isNotEmpty(menuTemplateEntitys)) {
                    List<MenuTemplateEntity> menuTemplateEntityList = menuTemplateEntitys.stream().
                            sorted(
                                    Comparator.comparing(MenuTemplateEntity::getPriorityLevel).reversed().
                                            thenComparing(Comparator.comparing(MenuTemplateEntity::getUpdateTime).reversed())
                            ).
                            collect(Collectors.toList());
                    entity = menuTemplateEntityList.get(0);
                }
            } else {
                entity = menuTemplateDao.getByEmployeeId(ei, employeeId, platform);
            }
            menuTemplateDao.removeEmployeeIdByStop(ei, employeeId);

            if (Objects.isNull(entity)) {
                return;
            }
            notifyChangeBySelfChange(ei, employeeId, entity, null);
        } else if (Objects.equals(newEmployeeDto.getStatus(), EmployeeEntityStatus.NORMAL)) {
            changeByDepartmentChange(ei, employeeId, newEmployeeDto.getMainDepartmentId(), platform);
        }
    }

    private void changeByEmployeeHasNull(Integer ei, Integer employeeId, EmployeeDto newEmployeeDto, Integer newMainDepartmentId, int platform) {
        //新建员工
        if (newEmployeeDto != null) {
            log.info("Add employee mq ei:{},employeeId:{},newEmployeeDto:{},newMainDepartmentId:{},platform:{}",
                    ei, employeeId, JSON.toJSONString(newEmployeeDto), newMainDepartmentId, platform);
            MenuTemplateEntity entity = addEmployeeByDepartmentId(ei, employeeId, newMainDepartmentId, platform);
            ArrayList<Integer> effectiveEmployeeIds = Lists.newArrayList(employeeId);
            naviAppChangeNotify.sendNaviAppChanged(ei, effectiveEmployeeIds, false);
            log.info("Add employee mq notify menu change start");
            if (Objects.nonNull(entity) || platform == 0) {
                appCustomizeMenuChangeNotify.notifyMenuChangeByCreateEmployee(ei, effectiveEmployeeIds, Objects.isNull(entity) ? null : entity.getMenus());
            }
            log.info("Add employee mq notify menu change end");
        }
    }

    private void changeByDepartmentChange(Integer ei, Integer employeeId, Integer newMainDepartmentId, int platform) {

        MenuTemplateEntity entity = new MenuTemplateEntity();
        if (uiPaasConfig.checkNewAppCustomer(ei) && configCoreProviderService.useNewAppMenuSearchFlag) {
            QueryScopeListRestArg queryScopeListRestArg = new QueryScopeListRestArg();
            queryScopeListRestArg.setTenantId(ei);
            queryScopeListRestArg.setEmployeeId(employeeId);
            List<Scope> userScopeList = tenantPageTempleRestService.queryScopeList(String.valueOf(ei), queryScopeListRestArg).getScopeList();
            List<String> listScope = EmployeeId.getScopeListStringByScopeList(userScopeList);
            List<MenuTemplateEntity> menuTemplateEntitys = menuTemplateDao.getMenuTemplateByScopeList(ei, platform, listScope);
            if (CollectionUtils.isNotEmpty(menuTemplateEntitys)) {
                List<MenuTemplateEntity> menuTemplateEntityList = menuTemplateEntitys.stream().
                        sorted(
                                Comparator.comparing(MenuTemplateEntity::getPriorityLevel).reversed().
                                        thenComparing(Comparator.comparing(MenuTemplateEntity::getUpdateTime).reversed())
                        ).
                        collect(Collectors.toList());
                entity = menuTemplateEntityList.get(0);
            }
        } else {
            entity = menuTemplateDao.getByEmployeeId(ei, employeeId, platform);
        }
        if (Objects.isNull(entity)) {
//                    没有旧的模板,只需要添加
            MenuTemplateEntity newEntity = addEmployeeByDepartmentId(ei, employeeId, newMainDepartmentId, platform);

            notifyChangeBySelfChange(ei, employeeId, null, newEntity);
            return;
        }

//                使用范围直接指定了该员工或者直接指定了新的部门,都不需要变动
        String roleIdsByEmployeeId = paaSRoleService.getRoleIdByEmployeeId(ei, employeeId);
        changeByHasOldTemplate(ei, employeeId, newMainDepartmentId, entity, roleIdsByEmployeeId, platform);
    }

    private void changeByHasOldTemplate(Integer ei, Integer employeeId, Integer newMainDepartmentId, MenuTemplateEntity entity, String roleIdsByEmployeeId, int platform) {
//        没有范围 或 没有指定 该员工/新部门/角色 需要修改
        boolean needChange = Objects.isNull(entity.getRange()) ||
                (notContains(entity.getRange().getEmployeeIds(), employeeId) && notContains(entity.getRange().getDepartmentIds(), newMainDepartmentId)
                        && notContains(entity.getRange().getRoleIds(), roleIdsByEmployeeId));
        if (needChange) {
            menuTemplateDao.removeEmployeeId(ei, entity.getMenuTemplateId(), employeeId);
            MenuTemplateEntity newEntity = addEmployeeByDepartmentId(ei, employeeId, newMainDepartmentId, roleIdsByEmployeeId, platform);

            notifyChangeBySelfChange(ei, employeeId, entity, newEntity);
        }
    }

    private static <T> boolean notContains(Collection<T> list, T t) {
        return CollectionUtils.isEmpty(list) || !list.contains(t);
    }

    private void notifyChangeBySelfChange(Integer ei, Integer employeeId, MenuTemplateEntity entity, MenuTemplateEntity newEntity) {
        ArrayList<Integer> employeeIds = Lists.newArrayList(employeeId);
        naviAppChangeNotify.sendNaviAppChanged(ei, employeeIds, false);
        pollingService.notifyPolling(ei, employeeIds, PollingService.appCustomPollingKey);
        appCustomizeMenuChangeNotify.notifyMenuChangeByMenu(ei, employeeIds, Objects.isNull(entity) ? null : entity.getMenus(), Objects.isNull(newEntity) ? null : newEntity.getMenus());

        log.info("organization change notifyChangeBySelfChange, ei:{}, empId:{}, old:{}, new:{}", ei, employeeId, Objects.isNull(entity) ? null : entity.getMenuTemplateId(), Objects.isNull(newEntity) ? null : newEntity.getMenuTemplateId());
    }

    private MenuTemplateEntity addEmployeeByDepartmentId(Integer ei, Integer employeeId, Integer newMainDepartmentId, int platform) {
        String roleId = paaSRoleService.getRoleIdByEmployeeId(ei, employeeId);
        return addEmployeeByDepartmentId(ei, employeeId, newMainDepartmentId, roleId, platform);
    }

    /**
     * 角色+部门有多个时,发企信
     * 两个都没有,查找上级部门对应的模板
     */
    private MenuTemplateEntity addEmployeeByDepartmentId(Integer ei, Integer employeeId, Integer newMainDepartmentId, String roleIdsByEmployeeId, int platform) {
        log.info("addEmployeeByDepartmentId arg,ei:{},employeeId:{},newMainDepartmentId:{},roleIdsByEmployeeId:{},platform:{}",
                ei, employeeId, newMainDepartmentId, roleIdsByEmployeeId, platform);
        //设置的员工优先级最高
        List<MenuTemplateEntity> entityList = menuTemplateDao.getBySettingEmployee(ei, employeeId, platform);
        if (CollectionUtils.isNotEmpty(entityList)) {
            MenuTemplateEntity menuTemplateEntity = addMenuTemplateEntity(ei, employeeId, entityList);
            log.info("addEmployeeByDepartmentId employee,ei:{},platform:{},addMenuTemplateEntity:{}", ei, platform, JSON.toJSONString(menuTemplateEntity));
            return menuTemplateEntity;
        }

        //没有设置员工,查部门和角色
        if (Objects.isNull(newMainDepartmentId)) {
            newMainDepartmentId = DepartmentDto.COMPANY_DEPARTMENT_ID;
        }

        List<MenuTemplateEntity> byDepartmentId = menuTemplateDao.getByRoleOrDepartmentId(ei, roleIdsByEmployeeId, newMainDepartmentId, platform);
        log.info("addEmployeeByDepartmentId getByRoleOrDepartmentId,ei:{},roleIdsByEmployeeId:{},newMainDepartmentId:{},platform:{}",
                ei, roleIdsByEmployeeId, newMainDepartmentId, platform);
        if (CollectionUtils.isNotEmpty(byDepartmentId)) {
            MenuTemplateEntity menuTemplateEntity = addMenuTemplateEntity(ei, employeeId, byDepartmentId);
            log.info("addEmployeeByDepartmentId department,ei:{},platform:{},addMenuTemplateEntity:{}", ei, platform, JSON.toJSONString(menuTemplateEntity));
            return menuTemplateEntity;
        }

        MenuTemplateEntity entity = null;
        //没有上级部门了
        if (Objects.equals(newMainDepartmentId, DepartmentDto.COMPANY_DEPARTMENT_ID)) {
            log.info("addEmployeeByDepartmentId not find upDepartment,ei:{},employeeId:{},platform:{}", ei, employeeId, platform);
            return null;
        }

        //到这说明角色和当前部门没有对应的模板, 找最近的上级部门
        List<Integer> allParentDepartment = organizationService.getAllParentDepartment(ei, newMainDepartmentId);
        List<MenuTemplateEntity> entities = menuTemplateDao.getByDepartmentId(ei, allParentDepartment, platform);

        //没有找到对应的模板,无需添加
        if (CollectionUtils.isEmpty(entities)) {
            log.info("addEmployeeByDepartmentId not find template,ei:{},employeeId:{},platform:{}", ei, employeeId, platform);
            return null;
        }

        //找最近的部门模板
        for (Integer parentDepartmentId : allParentDepartment) {
            entity = entities.stream().filter(m -> m.getRange().getDepartmentIds().contains(parentDepartmentId)).findFirst().orElse(null);
            if (Objects.nonNull(entity)) {
                break;
            }
        }

        if (Objects.nonNull(entity)) {
            log.info("addEmployeeByDepartmentId addEmployeeIdByCreate in menuTemplate ei:{},employeeId:{},entity:{},platform:{}",
                    ei, employeeId, JSON.toJSONString(entity), platform);
            menuTemplateDao.addEmployeeIdByCreate(ei, employeeId, entity.getMenuTemplateId());
        }
        log.info("addEmployeeByDepartmentId finally ei:{},platform:{},entity:{}", ei, platform, JSON.toJSONString(entity));
        return entity;
    }

    private MenuTemplateEntity addMenuTemplateEntity(Integer ei, Integer employeeId, List<MenuTemplateEntity> entityList) {
        if (entityList.size() > 1) {
            qiXinNotifyService.sendMessage(ei, employeeId, entityList);
            log.info("find entityList.size()={}", entityList.size());
            return null;
        }

        MenuTemplateEntity entity = entityList.get(0);
        menuTemplateDao.addEmployeeIdByCreate(ei, employeeId, entity.getMenuTemplateId());
        return entity;
    }

    @Override
    protected void onDepartmentChanged(DepartmentChangeEvent event) throws Throwable {
        int ei = event.getEnterpriseId();
        log.info("onDepartmentChanged,ei:{},body:{}", ei, JSON.toJSONString(event));
        //黑名单企业直接跳过
        if (ConfigCoreProviderService.isInEiBlacklistOnTreeChange(String.valueOf(ei))) {
            log.warn("onDepartmentChanged skip blacklist:{}", ei);
            return;
        }

        try {
            //通过context透传ei
            TraceContext.get()
                    .setTraceId(ConfigHelper.getProcessInfo().getName() + "/" + UUID.randomUUID().toString().replace("-", ""))
                    .setEi(String.valueOf(ei))
                    .setEa(event.getEnterpriseAccount());
            long startTime = System.currentTimeMillis();
            DepartmentDto newDepartmentDto = event.getNewDepartmentDto();
            DepartmentDto oldDepartmentDto = event.getOldDepartmentDto();
            if (Objects.nonNull(oldDepartmentDto) && oldDepartmentDto.getStatus() == DepartmentStatus.NORMAL && newDepartmentDto.getStatus() != DepartmentStatus.NORMAL) {
                menuTemplateDao.removeDepartmentIdByStop(ei, newDepartmentDto.getDepartmentId());
            }
            log.info("onDepartmentChanged.success,ei:{},cost:{}", ei, System.currentTimeMillis() - startTime);
        } finally {
            TraceContext.remove();
        }
    }

    /**
     *
     */
    @Override
    protected void onTreeChanged(TreeChangeEvent event) throws Throwable {
        try {
            int ei = event.getEnterpriseId();
            log.info("onTreeChanged,ei:{},body:{}", ei, JSON.toJSONString(event));

            //黑名单企业直接跳过
            if (ConfigCoreProviderService.isInEiBlacklistOnTreeChange(String.valueOf(ei))) {
                log.warn("onTreeChanged skip blacklist:{}", ei);
                return;
            }

            //通过context透传ei
            TraceContext.get()
                    .setTraceId(ConfigHelper.getProcessInfo().getName() + "/" + UUID.randomUUID().toString().replace("-", ""))
                    .setEi(String.valueOf(ei))
                    .setEa(event.getEnterpriseAccount());
            long startTime = System.currentTimeMillis();

//        int ei = event.getEnterpriseId();
//        List<Integer> changeDeptIds = event.getOldDepartmentMap().entrySet()
//                .stream()
//                .filter(entry -> {
//                    TreeChangeEvent.Department oldDepartment = entry.getValue();
//                    Integer departmentId = entry.getKey();
//                    TreeChangeEvent.Department newDepartment = event.getNewDepartmentMap().getOrDefault(departmentId, null);
//                    return checkParentChange(oldDepartment, newDepartment);
//                })
//                .map(Map.Entry::getKey)
//                .collect(Collectors.toList());
//
//        List<EmployeeDto> employeeDtos = organizationService.getEmployeeDtoByDepartmentIds(ei, changeDeptIds);

            List<Integer> affectedDepartmentIds = Lists.newArrayList();

            event.getOldDepartmentMap().forEach((departmentId, oldDepartment) ->
            {
                TreeChangeEvent.Department newDepartment = event.getNewDepartmentMap().getOrDefault(departmentId, null);
                if (newDepartment != null) {
                    String oldAncestors = StringUtils.join(oldDepartment.getAncestors().stream().sorted().collect(Collectors.toList()), ',');
                    String newAncestors = StringUtils.join(newDepartment.getAncestors().stream().sorted().collect(Collectors.toList()), ',');
                    if (!oldAncestors.equals(newAncestors)) {
                        affectedDepartmentIds.add(oldDepartment.getDepartmentId());
                    }
                }
            });
            int sizeForQueryUsersByDepts = configCoreProviderService.getSizeForQueryUsersByDepts();
            int sizeForQueryRolesByUsers = configCoreProviderService.getsSizeForQueryRolesByUsers();
            Lists.partition(affectedDepartmentIds, sizeForQueryUsersByDepts).forEach(part -> updateMenuTemplate(ei, part, sizeForQueryRolesByUsers));
            log.info("onTreeChanged.success,ei:{},cost:{}", ei, System.currentTimeMillis() - startTime);
        } finally {
            TraceContext.remove();
        }

    }

    private void updateMenuTemplate(int ei, List<Integer> affectedDepartmentIds, int sizeForQueryRolesByUsers) {
        List<EmployeeDto> affectedEmployees = organizationService.getEmployeeDtoByDepartmentIds(ei, affectedDepartmentIds, RunStatus.ACTIVE);
        Map<Integer, EmployeeDto> affectedEmployeeMap = affectedEmployees.stream().collect(Collectors.toMap(EmployeeDto::getEmployeeId, Function.identity()));
        Set<Integer> affectedEmployeeIds = affectedEmployees.stream().map(EmployeeDto::getEmployeeId).collect(Collectors.toSet());
        List<Integer> ids = Lists.newArrayList(affectedEmployeeIds);
        log.info("Organization.onTreeChanged.employeeIds {}", JSON.toJSONString(affectedEmployeeIds));
        for (int platform : configCoreProviderService.getLicenseList()) {
            Lists.partition(ids, sizeForQueryRolesByUsers).forEach(part -> {
                Map<Integer, String> roleMap = paaSRoleService.getRoleEmpMapByEmployeeIds(ei, part);
                updateMenuTemplateByPlatform(ei, affectedEmployeeMap, affectedEmployeeIds, part, platform, roleMap);
            });
        }
    }

    private void updateMenuTemplateByPlatform(int ei, Map<Integer, EmployeeDto> affectedEmployeeMap, Set<Integer> affectedEmployeeIds, List<Integer> ids, int platform, Map<Integer, String> roleMap) {
        List<MenuTemplateEntity> byEmployeeId = menuTemplateDao.getByEmployeeId(ei, ids, platform);
        Map<Integer, MenuTemplateEntity> empMenuTempMap = byEmployeeId.stream()
                .flatMap(m -> m.getEffectiveEmployeeIds().stream().filter(affectedEmployeeIds::contains).map(id -> Pair.of(id, m)))
                .collect(Collectors.toMap(Pair::getKey, Pair::getValue, (t1, t2) -> t1));

        affectedEmployeeIds.stream().distinct().forEach(employeeId ->
        {
            EmployeeDto employee = affectedEmployeeMap.getOrDefault(employeeId, null);
            if (employee != null) {
                Integer mainDepartmentId = employee.getMainDepartmentId();
                String roleIdsByEmployeeId = roleMap.get(employeeId);

                MenuTemplateEntity entity = empMenuTempMap.get(employeeId);
                if (Objects.isNull(entity)) {
//                    没有旧的模板,只需要添加
                    MenuTemplateEntity newEntity = addEmployeeByDepartmentId(ei, employeeId, mainDepartmentId, platform);

                    notifyChangeBySelfChange(ei, employeeId, null, newEntity);
                    return;
                }

//                使用范围直接指定了该员工或者直接指定了新的部门,都不需要变动
                changeByHasOldTemplate(ei, employeeId, mainDepartmentId, entity, roleIdsByEmployeeId, platform);
            }
        });
    }

    private boolean checkParentChange(TreeChangeEvent.Department oldDepartment, TreeChangeEvent.Department newDepartment) {
        return Objects.equals(getParent(oldDepartment), getParent(newDepartment));
    }

    private Integer getParent(TreeChangeEvent.Department department) {
        List<Integer> ancestors = department.getAncestors();
        return CollectionUtils.isEmpty(ancestors) ? null : ancestors.get(ancestors.size() - 1);
    }
}
