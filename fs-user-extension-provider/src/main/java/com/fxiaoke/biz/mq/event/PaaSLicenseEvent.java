package com.fxiaoke.biz.mq.event;

import lombok.Data;

import java.util.List;
import java.util.Set;

@Data
public class PaaSLicenseEvent {
    private String tenantId; //企业id
    private String licenseVersion; //产品编码
    private Set<String> moduleCodes; // 当前产品下对用的module
    private List<MqModulePara> mqModuleParas; //para信息
    private long createTime; //当前产品的创建时间
    private long startTime; //当前产品的开始时间
    private long expiredTime; // 当前产品的到期时间

    @Data
    public static class MqModulePara {
        private String moduleCode; // 对应的moduleCode
        private String paraKey;
        private String paraValue;
    }
}
