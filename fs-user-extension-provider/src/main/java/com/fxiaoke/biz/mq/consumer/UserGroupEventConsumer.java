package com.fxiaoke.biz.mq.consumer;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.biz.dao.MenuTemplateDao;
import com.fxiaoke.biz.dao.entity.MenuTemplateEntity;
import com.fxiaoke.biz.mq.event.PaaSUserGroupEvent;
import com.fxiaoke.biz.service.NotifyChangeService;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.google.common.base.Charsets;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.Message;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class UserGroupEventConsumer {
    private static final Logger logger = LoggerFactory.getLogger(UserGroupEventConsumer.class);
    private static final String CONFIG_NAME = "fs-user-app-user-group-consumer";

    @Resource
    private MenuTemplateDao menuTemplateDao;

    @Autowired
    private NotifyChangeService notifyChangeService;

    private AutoConfMQPushConsumer consumer;


    @PostConstruct
    public void init() {
        MessageListenerConcurrently listener = (messages, context) -> {
            messages.forEach(this::processMessage);
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        };
        consumer = new AutoConfMQPushConsumer(CONFIG_NAME, listener);
        consumer.start();
        logger.info("UserGroupEventConsumer init over");
    }

    @PreDestroy
    public void shutDown() {
        consumer.close();
    }

    public void processMessage(Message message) {
        try {
            handleObjectEvent(message.getBody());
        } catch (Throwable e) {
            logger.error("UserGroupEventConsumer error", e);
        }
    }

    private void handleObjectEvent(byte[] bytes) {
        try {
            String body = new String(bytes, Charsets.UTF_8);
            PaaSUserGroupEvent event = JSONObject.parseObject(body, PaaSUserGroupEvent.class);
            if (event == null || event.getType() != 2) { // 非用户组成员变更，暂不处理
                return;
            }
            if (event.getRelationIds() == null || event.getRelationIds().size() != 1) {
                logger.error("there is maybe invalid event:{}", event);
            }
            List<Integer> quitList = getEmployeeIdList(CollectionUtils.subtract(event.getOldMembers(), event.getNewMembers()));
            List<Integer> addList = getEmployeeIdList(CollectionUtils.subtract(event.getNewMembers(), event.getOldMembers()));
            removeEffectiveEmployeeId(Integer.valueOf(event.getTenantId()), event.getRelationIds(), quitList);
            addEffectiveEmployeeId(Integer.valueOf(event.getTenantId()), event.getRelationIds(), addList);
        } catch (Exception e) {
            logger.error("UserGroupEventConsumer", e);
        }
    }

    private List<Integer> getEmployeeIdList(Collection<String> employeeList) {
        List<Integer> ret = Lists.newArrayList();
        if (CollectionUtils.isEmpty(employeeList)) {
            return ret;
        }
        for (String employeeIdString : employeeList) {
            try {
                Integer employeeId = Integer.valueOf(employeeIdString);
                ret.add(employeeId);
            } catch (Exception e) {
                logger.error("there is unknown employeeIdString:{}", employeeIdString, e);
            }
        }
        return ret;
    }

    private void removeEffectiveEmployeeId(int tenantId, List<String> groupId, List<Integer> removeUserIds) {
        if (CollectionUtils.isEmpty(groupId) || CollectionUtils.isEmpty(removeUserIds)) {
            return;
        }
        List<MenuTemplateEntity> entities = menuTemplateDao.batchGetByRoleByUserGroup(tenantId, groupId.get(0), removeUserIds);
        if (CollectionUtils.isEmpty(entities)) {
            return;
        }
        List<String> templateIdList = Lists.newArrayList();
        for (MenuTemplateEntity entity : entities) {
            templateIdList.add(entity.getMenuTemplateId());
            menuTemplateDao.removeEmployeeIds(entity.getEnterpriseId(), entity.getMenuTemplateId(), removeUserIds);
            notifyChangeService.notifyChangeByEmployeeIds(entity.getEnterpriseId(), removeUserIds, entity, null);
        }
        logger.info("try to remove user for tenantId:{}, removeUserIds:{}, templateIdList:{}", tenantId, removeUserIds, templateIdList);
    }

    private void addEffectiveEmployeeId(int tenantId, List<String> groupId, List<Integer> addUserIds) {
        if (CollectionUtils.isEmpty(groupId) || CollectionUtils.isEmpty(addUserIds)) {
            return;
        }
        List<MenuTemplateEntity> entityList = menuTemplateDao.getByRoleByUserGroup(tenantId, groupId.get(0));
        if (entityList != null) {
            return;
        }
        //对适用平台进行分类
        Map<Integer, MenuTemplateEntity> entityMap = entityList.stream().collect(Collectors.toMap(MenuTemplateEntity::getPlatform, Function.identity(), (x, y) -> x));
        for (Map.Entry<Integer, MenuTemplateEntity> entry : entityMap.entrySet()) {
            List<MenuTemplateEntity> entities = menuTemplateDao.getByEmployeeId(tenantId, addUserIds, entry.getValue().getPlatform());
            Collection<Integer> effective = addUserIds;
            if (CollectionUtils.isNotEmpty(entities)) {
                for (MenuTemplateEntity otherEntity : entities) {
                    effective = CollectionUtils.subtract(effective, otherEntity.getEffectiveEmployeeIds());
                }
            }
            if (CollectionUtils.isEmpty(effective)) {
                return;
            }
            List<Integer> needToAdd = Lists.newArrayList(effective);
            MenuTemplateEntity entity = entry.getValue();
            logger.info("try to add user for tenantId:{}, add effective:{}, addUserIds:{}, id:{}", tenantId, needToAdd, addUserIds, entry.getValue());
            menuTemplateDao.addEmployeeIds(entity.getEnterpriseId(), entity.getMenuTemplateId(), needToAdd);
            notifyChangeService.notifyChangeByEmployeeIds(entity.getEnterpriseId(), needToAdd, null, entity);
        }
    }
}
