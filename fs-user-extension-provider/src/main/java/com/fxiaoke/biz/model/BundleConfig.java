package com.fxiaoke.biz.model;

import com.fxiaoke.api.model.GetBundleInfoDTO;
import org.apache.commons.lang3.StringUtils;

/**
 * Description
 * Created by lynxs on 2017/9/14.
 */
public class BundleConfig {
    private VersionRange versionRange;
    private String url;
    private String md5;
    private String h5url;
    private boolean weexEnabled = true;

    public boolean inRange(int version) {
        return versionRange.inRange(version);
    }

    public VersionRange getVersionRange() {
        return versionRange;
    }

    public void setVersionRange(final VersionRange versionRange) {
        this.versionRange = versionRange;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(final String url) {
        this.url = url;
    }

    public String getMd5() {
        return md5;
    }

    public void setMd5(final String md5) {
        this.md5 = md5;
    }

    public BundleConfig(final String range, final String url, final String md5, final String h5url, final Boolean weexEnabled) {
        this.versionRange = new VersionRange(StringUtils.defaultIfBlank(range,"(0-0)"));
        this.url = url;
        this.md5 = md5;
        this.h5url = h5url;
        if (weexEnabled != null) {
            this.weexEnabled = weexEnabled;
        }
    }

    public GetBundleInfoDTO.Bundle toBundleDTO(String bundleName) {
        final GetBundleInfoDTO.Bundle bundle = new GetBundleInfoDTO.Bundle();
        bundle.setName(bundleName);
        bundle.setMd5(md5);
        bundle.setUrl(url);
        bundle.setH5url(h5url);
        bundle.setWeexEnabled(weexEnabled);
        return bundle;
    }

    public static class VersionRange {
        int start = Integer.MIN_VALUE;
        int end = Integer.MIN_VALUE;
        boolean includeStart = false;
        boolean includeEnd = false;
        boolean isDefault = false;
        
        VersionRange(final String range) {
            if("default".equals(range)) {
                isDefault = true;
                this.end = Integer.MAX_VALUE;
                includeStart = true;
                includeEnd = true;
                return;
            }
            if(range.startsWith("[")) {
                this.includeStart = true;
            }
            if (range.endsWith("]")) {
                this.includeEnd = true;
            }
            final String[] split = range.substring(1, range.length() - 1).split("-");
            this.start = Integer.valueOf(split[0]);
            this.end = Integer.valueOf(split[1]);
        }

        public boolean inRange(int version) {
            if (version < start || (!includeStart && version == start)) {
                return false;
            }
            if (version > end || (!includeEnd && version == end)) {
                return false;
            }
            return true;
        }

        public boolean isDefault(){
            return isDefault;
        }
    }
}
