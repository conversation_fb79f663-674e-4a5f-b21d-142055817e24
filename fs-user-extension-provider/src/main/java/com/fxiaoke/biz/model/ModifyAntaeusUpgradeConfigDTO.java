package com.fxiaoke.biz.model;

import java.io.Serializable;

/**
 * Description
 * Created by shangrenpeng on 16/8/9.
 */
public class ModifyAntaeusUpgradeConfigDTO {

    public static class Arg implements Serializable {
        private static final long serialVersionUID = 6907251233230752988L;
        private String type;//iOS,ANDROID,iOS_TEST,ANDROID_TEST

        private String version; //5.4.3

        private long versionNumber;//该升级客户端的版本号,100543001

        private int subClientType;//NONE(0),HTML(1),ANDROID(2),IOS(3),WINDOWS(4),MAC(5);此处只能为安卓或iOS

        private String targetVersion;//升级目标的主版本号

        private long target;//升级弹窗的类型

        private boolean needUpdate = false;//是否需要升级

        private boolean forceUp; //是否强制升级

        private int notifyType; // 通知类型
        private String author;

        public static long getSerialVersionUID() {
            return serialVersionUID;
        }

        public String getType() {
            return type;
        }

        public void setType(final String type) {
            this.type = type;
        }

        public String getVersion() {
            return version;
        }

        public void setVersion(final String version) {
            this.version = version;
        }

        public long getVersionNumber() {
            return versionNumber;
        }

        public void setVersionNumber(final long versionNumber) {
            this.versionNumber = versionNumber;
        }

        public int getSubClientType() {
            return subClientType;
        }

        public void setSubClientType(final int subClientType) {
            this.subClientType = subClientType;
        }

        public String getTargetVersion() {
            return targetVersion;
        }

        public void setTargetVersion(final String targetVersion) {
            this.targetVersion = targetVersion;
        }

        public long getTarget() {
            return target;
        }

        public void setTarget(final long target) {
            this.target = target;
        }

        public boolean isNeedUpdate() {
            return needUpdate;
        }

        public void setNeedUpdate(final boolean needUpdate) {
            this.needUpdate = needUpdate;
        }

        public boolean isForceUp() {
            return forceUp;
        }

        public void setForceUp(final boolean forceUp) {
            this.forceUp = forceUp;
        }

        public int getNotifyType() {
            return notifyType;
        }

        public void setNotifyType(final int notifyType) {
            this.notifyType = notifyType;
        }

        @Override
        public String toString() {
            return "Arg{" +
                    "menuType='" + type + '\'' +
                    ", version='" + version + '\'' +
                    ", versionNumber=" + versionNumber +
                    ", subClientType=" + subClientType +
                    ", targetVersion='" + targetVersion + '\'' +
                    ", target=" + target +
                    ", needUpdate=" + needUpdate +
                    ", forceUp=" + forceUp +
                    ", notifyType=" + notifyType +
                    ", author='" + author + '\'' +
                    '}';
        }

        public String getAuthor() {
            return author;
        }

        public void setAuthor(final String author) {
            this.author = author;
        }
    }

    public static class Result implements Serializable{
        private static final long serialVersionUID = 8474408231954091114L;

        private String message = "ok";

        public String getMessage() {
            return message;
        }

        public void setMessage(final String message) {
            this.message = message;
        }

        @Override
        public String toString() {
            return "Result{" +
                    '}';
        }
    }
}
