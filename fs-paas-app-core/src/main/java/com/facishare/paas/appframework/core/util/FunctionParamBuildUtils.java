package com.facishare.paas.appframework.core.util;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class FunctionParamBuildUtils {

    public static Tuple<IObjectData, Map<String, List<IObjectData>>> getFunctionData(ObjectDataDocument masterData, ObjectDataDocument objectData, Map<String, List<ObjectDataDocument>> objectDetails) {
        if (CollectionUtils.notEmpty(masterData)) {
            IObjectData master = masterData.toObjectData();

            Map<String, List<IObjectData>> details = new HashMap<>();
            if (Objects.isNull(objectData)) {
                // 正常不会走到此逻辑
                return Tuple.of(master, new HashMap<>());
            }
            IObjectData detail = objectData.toObjectData();
            if (CollectionUtils.empty(objectDetails)) {
                //主从模式下(函数绑定从对象)网页不是返回全部的数据，而是只返回触发的那条从
                details.put(detail.getDescribeApiName(), Arrays.asList(detail));
            } else {
                List<ObjectDataDocument> detailList = objectDetails.get(detail.getDescribeApiName());
                List<IObjectData> detailDataList = CollectionUtils.nullToEmpty(detailList).stream()
                        .filter(CollectionUtils::notEmpty)
                        .map(ObjectDataDocument::toObjectData)
                        .collect(Collectors.toList());

                //前端会把当前操作的从放在第一条的位置，
                //保障从对象列表第一条从是当前操作的从数据，否则以前的自定义函数执行会有问题
                if (CollectionUtils.notEmpty(detailDataList)) {
                    detailDataList.set(0, detail);
                } else {
                    log.warn("getFunctionData masterData: {}, objectData: {}, objectDetails: {}", masterData, objectData, objectDetails);
                }

                details.put(detail.getDescribeApiName(), detailDataList);
            }

            return Tuple.of(master, details);
        } else {
            if (CollectionUtils.notEmpty(objectData)) {
                return Tuple.of(objectData.toObjectData(), new HashMap<>());
            }
            return Tuple.of(new ObjectData(), new HashMap<>());
        }
    }
}
