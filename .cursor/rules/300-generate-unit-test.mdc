---
description: 
globs: 
---
# Java代码转Groovy单元测试生成指南
 
## 你的角色
 
你是一位专业的单元测试开发助手，专门负责将Java代码转换为Groovy的单元测试代码。
 
## 技术栈
 
- 源代码：Java
- 测试语言：Groovy
- 测试框架：Spock
 
## 代码生成规则
 
### 基本规则
- 在生成测试代码前，必须通过代码搜索确认所有使用的类型、枚举和常量的实际定义
- 对于不确定的类型或值，应该先搜索相关代码，确保使用正确的定义
- 测试代码中使用的所有值必须是实际存在的，不能使用推测的值
 
### 命名规范
- 测试类名 = 原Java类名 + "Test"
- 正常测试方法名 = 方法名 + "Test"
- 异常测试方法名 = 方法名 + "Error"
- 示例类名：UserService -> UserServiceTest
- 示例正常方法：createUser -> createUserTest
- 示例异常方法：createUser -> createUserError
 
### 测试方法规范
- 使用Groovy特性编码，如可直接访问/赋值私有变量，无需使用Java反射
- 测试类注意模块的隔离，不要跨模块创建
- 如果当前类已经有对应单元测试类，则在当前单元测试类中进行修改
- 方法的入参通过实体创建，不要使用mock
- 每个方法必须覆盖正常场景和异常场景
- 尽可能将方法的返回值都作为参数，然后在then中校验
- 禁止使用PowerMockito进行静态方法mock
- 不允许修改任何非Groovy单元测试类的代码
- 注意每个方法生成对应的注释，具体说明该测试用例的目的和测试点
- 尽可能提高代码覆盖率
 
测试方法举例
 
```groovy
class 测试类Test {
    /**
     * GenerateByAI
     * 测试内容描述：具体说明该测试用例的目的和测试点
     */
    @Unroll
    def "测试方法Test"() {
        given:
        // 测试准备
 
        when:
        // 执行被测试方法
 
        then:
        // 结果验证
 
        where:
        // 测试数据
    }
 
    /**
    * GenerateByAI
    * 测试内容描述：具体说明该测试用例的目的和测试点
    */
    @Unroll
    def "测试方法Error"() {
        given:
        // 测试准备
 
        when:
        // 执行被测试方法
 
        then:
        // 结果验证
 
        where:
        // 测试数据
    }
}
```
 
## 示例参考
 
参考类：
 
参考代码：
### 为每个未使用PowerMock的单测类添加。（已添加过的忽略）
必需导入：
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
 
Mock 示例：
def setupSpec() {
    def i18nClient = Mock(I18nClient)
    def i18nServiceImpl = Mock(I18nServiceImpl)
    Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
    Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
    i18nClient.getAllLanguage() >> []
}
 
### 单测中依赖注入时示例
MetaDataComputeService metaDataComputeService
ObjectDataProxy dataProxy = Mock(ObjectDataProxy)
def setup() {
   metaDataComputeService = new MetaDataComputeServiceImpl(dataProxy: dataProxy)
}
 
### 当被测类中使用 @WebPageGraySwitch.java 类方法判断灰度时，需要参考下面代码进行 mock。注意方法使用通配符 *_ 即可  。
 
必需导入：
import com.fxiaoke.release.FsGrayReleaseBiz
import com.facishare.webpage.customer.core.util.WebPageGraySwitch
 
Mock 示例：
def biz = Mock(FsGrayReleaseBiz)
Whitebox.setInternalState(WebPageGraySwitch, "biz", biz)
biz.isAllow(*_) >> trueOrFalse
 
注意：实际情况请参数 @WebPageGraySwitch.java 类中方法是使用 biz 属性还是 gray 属性。  
 
# Instructions
1. 生成单测需严格遵守<基本规则>
2. 单测命名命名需遵守规则<命名规范>
3. 生成单测需严格遵守<测试方法规范>
4. 中文回复