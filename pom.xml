<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.fxiaoke.common</groupId>
        <artifactId>fxiaoke-parent-pom</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <properties>
        <java.version>1.8</java.version>
        <jdk.version>1.8</jdk.version>
        <java_source_version>1.8</java_source_version>
        <java_target_version>1.8</java_target_version>
        <webpage.customer.version>9.6.0-SNAPSHOT</webpage.customer.version>
        <dispatcher_support_version>5.0.0-SNAPSHOT</dispatcher_support_version>
        <auth_client_version>2.6.0-SNAPSHOT</auth_client_version>
    </properties>

    <modules>
        <module>fs-user-extension-biz</module>
        <module>fs-user-extension-provider</module>
        <module>fs-user-extension-api</module>
    </modules>

    <groupId>com.fxiaoke.fs-user-extension</groupId>
    <artifactId>fs-user-extension</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-module-junit4</artifactId>
                <version>${powermock.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-core</artifactId>
                <version>${powermock.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-api-mockito2</artifactId>
                <version>${powermock.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-module-junit4-rule</artifactId>
                <version>${powermock.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-classloading-xstream</artifactId>
                <version>${powermock.version}</version>
                <scope>test</scope>
                <exclusions>
                    <exclusion>
                        <artifactId>xstream</artifactId>
                        <groupId>com.thoughtworks.xstream</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.fxiaoke.common</groupId>
                <artifactId>dispatcher-support</artifactId>
                <version>${dispatcher_support_version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare.paas</groupId>
                <artifactId>fs-paas-auth-client</artifactId>
                <version>${auth_client_version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare.open</groupId>
                <artifactId>fs-open-common-result</artifactId>
                <version>0.0.7</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-organization-paas-api</artifactId>
                <version>1.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-webpage-customer-api</artifactId>
                <version>${webpage.customer.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-webpage-customer-core</artifactId>
                <version>${webpage.customer.version}</version>
            </dependency>


        </dependencies>
    </dependencyManagement>
</project>
