package com.facishare.paas.appframework.function.dto;

import com.facishare.paas.appframework.function.dto.FindRelationBySource.FuncRelationInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * create by <PERSON><PERSON><PERSON> on 2021/07/14
 */
public interface SaveRelation {

    @Data
    @NoArgsConstructor
    @AllArgsConstructor(staticName = "of")
    class Arg {
        private List<FuncRelationInfo> items;
    }

    @Data
    class Result {
        private Integer code;
        private String message;
    }
}
