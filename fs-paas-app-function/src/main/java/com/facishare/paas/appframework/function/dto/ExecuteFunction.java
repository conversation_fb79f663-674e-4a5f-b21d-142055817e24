package com.facishare.paas.appframework.function.dto;

import com.facishare.function.Function;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Created by fengjy in 2019/12/30 11:11
 */
public interface ExecuteFunction {
    @Data
    @Builder
    class Arg {
        Function function;
        //主对象数据
        Map<String, Object> objectData;
        //从对象数据
        Map<String, List<Map<String, Object>>> objectDetails;
        /**
         * 相关对象数据
         */
        Map<String, List<Map<String, Object>>> objectRelatedData;
        //Action执行参数
        Map<String, Object> arg;
        String actionStage;
        String apiName;
        String actionPage;
        Map<String, Object> searchQuery;
    }

}
