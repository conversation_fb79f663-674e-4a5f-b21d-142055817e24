package com.facishare.job.schedule.jaxrs.provider;

import com.facishare.job.schedule.exception.BusinessException;
import com.facishare.job.schedule.jaxrs.model.RequestContext;
import com.facishare.job.schedule.jaxrs.model.RequestContextManager;
import com.facishare.job.schedule.jaxrs.model.RestAPIResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.ws.rs.core.Response;
import javax.ws.rs.ext.ExceptionMapper;
import javax.ws.rs.ext.Provider;

@Slf4j
@Provider
@Component
public class BusinessExceptionHandler implements ExceptionMapper<BusinessException> {

    @Override
    public Response toResponse(BusinessException e) {
        RequestContext context = RequestContextManager.getContext();
        log.warn("context:{} ; error:{}", context, e.getMessage(), e);
        return Response.status(200)
                .entity(RestAPIResult.fail(e.getCode(), e.getMessage()))
                .build();
    }
}
