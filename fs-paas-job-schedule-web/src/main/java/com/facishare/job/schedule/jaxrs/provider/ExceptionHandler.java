package com.facishare.job.schedule.jaxrs.provider;

import com.facishare.job.schedule.jaxrs.model.RequestContext;
import com.facishare.job.schedule.jaxrs.model.RequestContextManager;
import com.facishare.job.schedule.jaxrs.model.RestAPIResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.ws.rs.core.Response;
import javax.ws.rs.ext.ExceptionMapper;
import javax.ws.rs.ext.Provider;

@Slf4j
@Provider
@Component
public class ExceptionHandler implements ExceptionMapper<Exception> {

    @Override
    public Response toResponse(Exception e) {
        RequestContext context = RequestContextManager.getContext();
        log.error("context:{} ; error:{}", context, e.getMessage(), e);
        return Response.status(500)
                .entity(RestAPIResult.fail(500, e.getMessage()))
                .build();
    }
}
