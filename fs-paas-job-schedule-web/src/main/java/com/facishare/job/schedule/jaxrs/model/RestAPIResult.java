package com.facishare.job.schedule.jaxrs.model;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class RestAPIResult {
    private static final int SUCCESS_CODE = 0;
    private static final String OK = "OK";

    private int code;
    private String message;
    private Object data;

    public static RestAPIResult success(Object result) {
        return RestAPIResult.builder().code(SUCCESS_CODE).message(OK).data(result).build();
    }

    public static RestAPIResult fail(int errCode, String errMessage) {
        return RestAPIResult.builder().code(errCode).message(errMessage).build();
    }
}
