package com.facishare.job.schedule.jaxrs.provider;

import com.facishare.job.schedule.jaxrs.annotation.RestAPI;
import com.facishare.job.schedule.jaxrs.model.RestAPIResult;
import org.springframework.stereotype.Component;

import javax.annotation.Priority;
import javax.ws.rs.container.ContainerRequestContext;
import javax.ws.rs.container.ContainerResponseContext;
import javax.ws.rs.container.ContainerResponseFilter;
import javax.ws.rs.ext.Provider;
import java.util.Objects;

@RestAPI
@Priority(100)
@Provider
@Component
public class RestResponseFilter implements ContainerResponseFilter {
    @Override
    public void filter(ContainerRequestContext requestContext, ContainerResponseContext responseContext) {
        Object entity = responseContext.getEntity();
        if (Objects.isNull(entity)) {
            responseContext.setEntity(RestAPIResult.success(entity));
        } else if (!(entity instanceof RestAPIResult)) {
            responseContext.setEntity(RestAPIResult.success(entity));
        }
    }
}
