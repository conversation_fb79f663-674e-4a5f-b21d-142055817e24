<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <appender name="jobScheduleLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${catalina.home}/logs/jobSchedule.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${catalina.home}/logs/jobSchedule.%d{yyyy-MM-dd}.log.zip</fileNamePattern>
            <maxHistory>7</maxHistory>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <!-- 日志中默认打印traceId和userId，方便定位问题-->
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{12} %X{traceId} %X{userId} %msg%n
            </pattern>
        </encoder>
    </appender>

    <appender name="REST" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <File>${catalina.home}/logs/rest.log</File>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %-5level %logger{36}:%L - %msg%n</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${catalina.home}/logs/rest.%d{yyyy-MM-dd}.log</FileNamePattern>
            <MaxHistory>3</MaxHistory>
            <CleanHistoryOnStart>true</CleanHistoryOnStart>
        </rollingPolicy>
    </appender>


    <appender name="SendMessage" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${catalina.home}/logs/sendMessageLog.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${catalina.home}/logs/sendMessageLog.%d{yyyy-MM-dd}.log.zip</fileNamePattern>
            <maxHistory>3</maxHistory>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <!-- 日志中默认打印traceId和userId，方便定位问题-->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{12} %X{traceId} %X{userId} %msg%n
            </pattern>
        </encoder>
    </appender>

    <appender name="Error" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <file>${catalina.home}/logs/error.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${catalina.home}/logs/error.%d{yyyy-MM-dd}.log.zip</fileNamePattern>
            <maxHistory>7</maxHistory>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <!-- 日志中默认打印traceId和userId，方便定位问题-->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{12} %X{traceId} %X{userId} %m%n
            </pattern>
        </encoder>
    </appender>

    <appender name="Access" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${catalina.home}/logs/fcp-access.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${catalina.home}/logs/fcp-access.%d{yyyy-MM-dd}.log.zip</fileNamePattern>
            <maxHistory>3</maxHistory>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <!-- 日志中默认打印traceId和userId，方便定位问题-->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{12} %X{traceId} %X{userId} %m%n
            </pattern>
        </encoder>
    </appender>

    <appender name="OSS_Trace" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${catalina.home}/logs/trace.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${catalina.home}/logs/trace.%d{yyyy-MM-dd}.log.zip</fileNamePattern>
            <maxHistory>3</maxHistory>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <!-- 日志中默认打印traceId和userId，方便定位问题-->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{12} %X{traceId} %X{userId} %msg%n
            </pattern>
        </encoder>
    </appender>

    <appender name="PerfLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${catalina.home}/logs/perf.log</File>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{12} %X{traceId} %X{userId} %msg%n</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${catalina.home}/logs/perf.%d{yyyy-MM-dd}.log</FileNamePattern>
            <MaxHistory>7</MaxHistory>
            <CleanHistoryOnStart>true</CleanHistoryOnStart>
        </rollingPolicy>
    </appender>


    <appender name="JOBSCHEDULE_ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>2048</queueSize>
        <appender-ref ref="jobScheduleLog"/>
    </appender>

    <appender name="OSS_ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>2048</queueSize>
        <appender-ref ref="OSS_Trace"/>
    </appender>

    <logger name="com.github.trace" level="INFO" additivity="false">
        <appender-ref ref="OSS_ASYNC"/>
    </logger>

    <logger name="com.facishare.fcp.log.FcpAccessLogger" level="INFO" additivity="false">
        <appender-ref ref="Access"/>
    </logger>

    <logger name="com.facishare.fcp.util.StopWatch" level="INFO" additivity="false">
        <appender-ref ref="PerfLog"/>
    </logger>

    <logger name="com.facishare.rest" level="WARN" additivity="false">
        <appender-ref ref="JOBSCHEDULE_ASYNC"/>
        <appender-ref ref="Error"/>
    </logger>

    <logger name="com.facishare.rest" level="INFO" additivity="false">
        <appender-ref ref="REST"/>
        <appender-ref ref="Error"/>
    </logger>


    <logger name="com.facishare.job" level="INFO" additivity="false">
        <appender-ref ref="JOBSCHEDULE_ASYNC"/>
        <appender-ref ref="Error"/>
    </logger>


    <logger name="SendMessageLog" level="INFO" additivity="false">
        <appender-ref ref="SendMessage"/>
        <appender-ref ref="Error"/>
    </logger>

    <root level="WARN">
        <appender-ref ref="SMARTFORM_ASYNC"/>
    </root>

</configuration>
