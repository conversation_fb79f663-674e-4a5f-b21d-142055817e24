<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
                           http://www.springframework.org/schema/beans/spring-beans.xsd">

    <!-- 1. 配置Job类 -->
    <bean id="scheduleTask" class="com.facishare.job.schedule.biz.task.ScheduleTask"/>
    <bean id="functionTask" class="com.facishare.job.schedule.biz.task.FunctionTask"/>
    <bean id="functionNotifyTask" class="com.facishare.job.schedule.biz.task.FunctionNotifyTask"/>
    <bean id="migrationTask" class="com.facishare.job.schedule.biz.task.MigrationTask"/>
    <bean id="realTimeTask" class="com.facishare.job.schedule.biz.task.RealTimeTask"/>
    <bean id="bulkPrintTask" class="com.facishare.job.schedule.biz.task.BulkPrintTask"/>
    <!-- 1. 添加BatchExportTask的bean定义 -->
    <bean id="batchExportTask" class="com.facishare.job.schedule.biz.task.BatchExportTask"/>

    <bean id="forceEndCallBackTask" class="com.facishare.job.schedule.biz.task.base.ForceEndCallBackTask"/>

    <bean id="bulkActionForceEndProcessingJobTask" class="com.facishare.job.schedule.biz.task.base.ForceEndProcessingJobTask">
        <property name="jobTypes">
            <list>
                <value>BULK_ACTION</value>
                <value>BULK_PRINT</value>
            </list>
        </property>
        <property name="forceEndIntervalMinute" value="5"/>
    </bean>

    <bean id="formulaManualJob" class="com.facishare.job.schedule.biz.task.ScanFormulaManualJob"/>
    <bean id="systemUpdateJobStatus" class="com.facishare.job.schedule.biz.task.base.ForceEndProcessingJobTask">
        <property name="excludeJobTypes">
            <list>
                <value>BULK_ACTION</value>
                <value>BULK_PRINT</value>
                <value>FUNCTION</value>
                <value>MIGRATION</value>
            </list>
        </property>
        <property name="forceEndIntervalMinute" value="120"/>
    </bean>


    <bean id="scheduleMethod" class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
        <property name="targetObject" ref="scheduleTask"/>
        <property name="targetMethod" value="execute"/>
        <property name="concurrent" value="false"/>
    </bean>

    <bean id="bulkPrintMethod" class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
        <property name="targetObject" ref="bulkPrintTask"/>
        <property name="targetMethod" value="execute"/>
        <property name="concurrent" value="false"/>
    </bean>

    <bean id="functionMethod" class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
        <property name="targetObject" ref="functionTask"/>
        <property name="targetMethod" value="execute"/>
        <property name="concurrent" value="false"/>
    </bean>

    <bean id="functionNotifyMethod" class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
        <property name="targetObject" ref="functionNotifyTask"/>
        <property name="targetMethod" value="execute"/>
        <property name="concurrent" value="false"/>
    </bean>

    <bean id="migrationMethod" class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
        <property name="targetObject" ref="migrationTask"/>
        <property name="targetMethod" value="execute"/>
        <property name="concurrent" value="false"/>
    </bean>

    <!-- 2. 添加BatchExportTask的Method配置 -->
    <bean id="batchExportMethod" class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
        <property name="targetObject" ref="batchExportTask"/>
        <property name="targetMethod" value="execute"/>
        <property name="concurrent" value="false"/>
    </bean>

    <bean id="forceEndCallBackMethod" class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
        <property name="targetObject" ref="forceEndCallBackTask"/>
        <property name="targetMethod" value="execute"/>
        <property name="concurrent" value="false"/>
    </bean>

    <!--配置Button JobDetail-->
    <bean id="realTimeTaskMethod" class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
        <!-- 执行目标job -->
        <property name="targetObject" ref="realTimeTask"/>

        <!-- 要执行的方法 -->
        <property name="targetMethod" value="execute"/>
        <property name="concurrent" value="false"/>
    </bean>


    <bean id="formulaManualJobMethod" class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
        <!-- 执行目标job -->
        <property name="targetObject" ref="formulaManualJob"/>

        <!-- 要执行的方法 -->
        <property name="targetMethod" value="execute"/>
        <property name="concurrent" value="false"/>
    </bean>

    <!--配置Button JobDetail-->
    <bean id="bulkActionForceEndProcessingJobMethod"
          class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
        <!-- 执行目标job -->
        <property name="targetObject" ref="bulkActionForceEndProcessingJobTask"/>

        <!-- 要执行的方法 -->
        <property name="targetMethod" value="execute"/>
        <property name="concurrent" value="false"/>
    </bean>

    <!-- 配置Button tirgger触发器 -->
    <bean id="bulkActionForceEndProcessingJobMethodCronTrigger"
          class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
        <!-- jobDetail -->
        <property name="jobDetail" ref="bulkActionForceEndProcessingJobMethod"/>

        <!-- cron表达式，执行时间  每秒执行一次 -->
        <property name="cronExpression" value="0 0/1 * * * ?"/>
    </bean>

    <!-- 配置Button tirgger触发器 -->
    <bean id="realTimeTaskMethodCronTrigger" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
        <!-- jobDetail -->
        <property name="jobDetail" ref="realTimeTaskMethod"/>

        <!-- cron表达式，执行时间  每秒执行一次 -->
        <property name="cronExpression" value="0/1 * * * * ?"/>
    </bean>


    <bean id="systemUpdateJobStatusMethod"
          class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
        <property name="targetObject" ref="systemUpdateJobStatus"/>
        <property name="targetMethod" value="execute"/>
        <property name="concurrent" value="false"/>
    </bean>


    <bean id="scheduleJobMethodCronTrigger" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
        <!-- jobDetail -->
        <property name="jobDetail" ref="scheduleMethod"/>
        <property name="cronExpression" value="0/3 * * * * ?"/>
    </bean>

    <bean id="bulkPrintJobMethodCronTrigger" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
        <!-- jobDetail -->
        <property name="jobDetail" ref="bulkPrintMethod"/>
        <property name="cronExpression" value="0/1 * * * * ?"/>
    </bean>

    <bean id="functionMethodCronTrigger" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
        <!-- jobDetail -->
        <property name="jobDetail" ref="functionMethod"/>
        <property name="cronExpression" value="0/1 * * * * ?"/>
    </bean>

    <bean id="functionNotifyMethodCronTrigger" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
        <!-- jobDetail -->
        <property name="jobDetail" ref="functionNotifyMethod"/>
        <property name="cronExpression" value="0 0/1 * * * ?"/>
    </bean>


    <bean id="migrationMethodCronTrigger" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
        <!-- jobDetail -->
        <property name="jobDetail" ref="migrationMethod"/>
        <property name="cronExpression" value="0/3 * * * * ?"/>
    </bean>


    <bean id="forceEndCallBackMethodCronTrigger" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
        <!-- jobDetail -->
        <property name="jobDetail" ref="forceEndCallBackMethod"/>
        <property name="cronExpression" value="0 0/1 * * * ?"/>
    </bean>

    <bean id="formulaManualJobMethodCronTrigger" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
        <!-- jobDetail -->
        <property name="jobDetail" ref="formulaManualJobMethod"/>
        <!-- cron表达式，执行时间  每60秒执行一次 -->
        <property name="cronExpression" value="0/10 * * * * ?"/>
    </bean>

    <bean id="systemUpdateJobStatusMethodCronTrigger"
          class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
        <!-- jobDetail  每30分钟执行一次-->
        <property name="jobDetail" ref="systemUpdateJobStatusMethod"/>
        <property name="cronExpression" value="0 0/30 * * * ?"/>
    </bean>

    <!-- 3. 添加BatchExportTask的触发器配置 -->
    <bean id="batchExportMethodCronTrigger" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
        <property name="jobDetail" ref="batchExportMethod"/>
        <property name="cronExpression" value="0 0/1 * * * ?"/>
    </bean>

    <!-- 4. 配置调度工厂 -->
    <bean id="springJobSchedulerFactoryBean" class="org.springframework.scheduling.quartz.SchedulerFactoryBean">
        <property name="triggers">
            <list>
                <ref bean="functionMethodCronTrigger"/>
                <ref bean="functionNotifyMethodCronTrigger"/>
                <ref bean="migrationMethodCronTrigger"/>
                <ref bean="scheduleJobMethodCronTrigger"/>
                <ref bean="bulkPrintJobMethodCronTrigger"/>
                <ref bean="formulaManualJobMethodCronTrigger"/>
                <ref bean="systemUpdateJobStatusMethodCronTrigger"/>
                <ref bean="realTimeTaskMethodCronTrigger"/>
                <ref bean="forceEndCallBackMethodCronTrigger"/>
                <ref bean="bulkActionForceEndProcessingJobMethodCronTrigger"/>
                <ref bean="batchExportMethodCronTrigger"/>
            </list>
        </property>
        <property name="configLocation" value="classpath:spring/quartz.properties"/>
    </bean>


</beans>