<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean id="restServiceProxyFactory" class="com.facishare.rest.core.RestServiceProxyFactory"
          p:configName="fs-pass-job-schedule-rest" init-method="init"/>

    <bean id="orgServiceProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.job.schedule.biz.service.ObjectDataScanProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>


    <bean id="metaDataStaticResource" class="com.facishare.rest.core.RestServiceProxyFactoryBean" p:type="com.facishare.job.schedule.biz.service.MetaDataStaticResource" >
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>


</beans>