<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

    <context:component-scan base-package="com.facishare.job"/>
    <context:component-scan base-package="com.facishare.idempotent"/>

    <import resource="classpath:spring/auto-conf.xml"/>
    <import resource="classpath:spring/mongo-db.xml"/>
    <import resource="classpath:spring/rocketmq.xml"/>
    <import resource="classpath:spring/scan-conf.xml"/>
    <import resource="classpath:spring/spring-quartz.xml"/>
    <import resource="classpath:spring/rest-proxy.xml"/>
    <import resource="classpath:spring/redis-support.xml"/>
    <import resource="classpath*:/META-INF/spring/pod-api-client.xml"/>
    <import resource="classpath:META-INF/spring/fs-qixin-rate-limiter.xml"/>

    <!--ei/ea转换-->
    <import resource="classpath:META-INF/spring/fs-qixin-enterprise-converter.xml"/>

    <bean id="dbMetricService" class="com.fxiaoke.biz.rest.service.DbMetricServiceImpl"/>
    <bean id="httpSupport" class="com.fxiaoke.common.http.spring.HttpSupportFactoryBean"/>
    <bean id="applicationInitListener" class="com.facishare.job.schedule.biz.task.base.ApplicationInitListener"/>
    <bean class="com.fxiaoke.metrics.MetricsConfiguration"/>
</beans>