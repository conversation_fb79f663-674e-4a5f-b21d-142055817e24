package com.facishare.open.app.ad.api.enums;

/**
 * APP可见类型枚举类
 *
 * <AUTHOR>
 * @date 2015年8月20日
 */
public enum ModuleKeyEnum {
    BANNERS("MK_BANNERS"),
    COMPONENTS("MK_COMPONENTS"),
    BEST_APPS("MK_BEST_APPS"),
    SERVICE_NUMBER("MK_SERVICE_NUMBER"), //服务号模块
    OUTER_SERVICE("MK_OUTER_SERVICE"); //外联服务号模块.
    
    private final String moduleKey;

    ModuleKeyEnum(String moduleKey) {
        this.moduleKey = moduleKey;
    }

    public String getModuleKey() {
        return moduleKey;
    }

    @Override
    public String toString() {
        return "ModuleKeyEnum{" +
                "moduleKey='" + moduleKey + '\'' +
                '}';
    }

    public static ModuleKeyEnum getByModuleKey(String moduleKey) {
        for (ModuleKeyEnum moduleKeyEnum : ModuleKeyEnum.values()) {
            if (moduleKeyEnum.getModuleKey().equals(moduleKey)) {
                return moduleKeyEnum;
            }
        }
        return null;
    }
}
