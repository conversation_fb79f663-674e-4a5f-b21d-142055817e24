package com.facishare.open.app.pay.mq.api.item;

import com.facishare.open.app.pay.mq.api.item.base.ProtoBase;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * Created by linchf on 2017/7/13.
 */
@Setter
@Getter
@ToString
public class RepealExperienceAccountCopyDataItem extends ProtoBase implements Serializable {
    private static final long serialVersionUID = -6104211249127229487L;

    public static final int FLAG = 2;

    /**
     * 废除体验账号的企业id
     */
    @Tag(1)
    private int repealEnterpriseId;

    @Tag(2)
    private String repealEnterpriseAccount;

    public RepealExperienceAccountCopyDataItem() {
    }

    public RepealExperienceAccountCopyDataItem(int repealEnterpriseId, String repealEnterpriseAccount) {
        this.repealEnterpriseId = repealEnterpriseId;
        this.repealEnterpriseAccount = repealEnterpriseAccount;
    }
}
