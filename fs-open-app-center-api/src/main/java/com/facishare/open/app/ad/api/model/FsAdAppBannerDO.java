package com.facishare.open.app.ad.api.model;

import java.io.Serializable;
import java.util.Date;

/**
 * 应用相关的banner位.
 *
 * <AUTHOR>
 * @date on 2016/01/07
 */
public class FsAdAppBannerDO implements Serializable {

    private String id;
    private String appId;
    private String targetUrl;
    private String imageUrl;
    private String description;
    private Integer index;
    private Integer status;
    private Date gmtCreate;
    private Date gmtModified;
    /**
     * 类型 app 侧,web侧
     */
    private Integer bannerType;

    /**
     * 跳转类型 1.应用详情 2.h5链接
     */
    private Integer targetType;

    public FsAdAppBannerDO() {
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    /**
     * 获取应用id
     */
    public String getAppId() {
        return appId;
    }

    /**
     * 设置应用id
     */
    public void setAppId(String appId) {
        this.appId = appId;
    }

    /**
     * 获取跳转地址
     */
    public String getTargetUrl() {
        return targetUrl;
    }

    /**
     * 设置跳转地址
     */
    public void setTargetUrl(String targetUrl) {
        this.targetUrl = targetUrl;
    }

    /**
     * 获取图片地址
     */
    public String getImageUrl() {
        return imageUrl;
    }

    /**
     * 设置图片地址
     */
    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    /**
     * 获取描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 设置描述
     */
    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * 获取排序字段
     */
    public Integer getIndex() {
        return index;
    }

    /**
     * 设置排序字段
     */
    public void setIndex(Integer index) {
        this.index = index;
    }

    /**
     * 获取状态
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * 设置状态
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * 获取创建时间
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     * 设置创建时间
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     * 获取最后修改时间
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     * 设置最后修改时间
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public Integer getBannerType() {
        return bannerType;
    }

    public void setBannerType(Integer bannerType) {
        this.bannerType = bannerType;
    }

    public Integer getTargetType() {
        return targetType;
    }

    public void setTargetType(Integer targetType) {
        this.targetType = targetType;
    }
}
