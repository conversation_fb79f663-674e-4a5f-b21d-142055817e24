package com.facishare.open.app.ad.api.service;

import com.facishare.open.app.ad.api.model.FsAdBestAppDO;
import com.facishare.open.common.result.BaseResult;

import java.util.List;

/**
 * 应用中心的精品应用服务.
 *
 * <AUTHOR>
 * @date on 2016/01/07
 */
public interface AppCenterBestAppService {

    /**
     * 查询精品应用的数据.
     *
     * @return 返回精品应用列表.
     */
    BaseResult<List<FsAdBestAppDO>> queryAppBestApps();

    /**
     * 查询一个位置
     *
     * @param index
     * @return
     */
    BaseResult<FsAdBestAppDO> queryByIndex(Integer index);

    /**
     * 添加一个精品应用
     *
     * @param appId
     * @param index
     * @return
     */
    BaseResult<String> addBestApp(String appId,Integer index);

    /**
     * 删除一个精品应用
     *
     * @param id
     * @return
     */
    BaseResult<Void> deleteBestApp(String id);

    /**
     * 修改精品应用排序号
     *
     * @param id 推荐位id.
     * @return
     */
    BaseResult<Void> updateBestAppIndex(String id,Integer index);
}
