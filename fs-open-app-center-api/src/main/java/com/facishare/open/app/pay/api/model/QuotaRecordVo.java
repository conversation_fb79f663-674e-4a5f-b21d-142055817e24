package com.facishare.open.app.pay.api.model;

import com.facishare.open.app.pay.api.enums.QuotaType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 企业购买应用配额的记录.
 * Created by xialf on 1/27/16.
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class QuotaRecordVo implements Serializable {
    /**
     * 配额记录id(无意义).
     */
    private Long id;

    /**
     * 公司账号account.
     */
    private String fsEa;

    /**
     * 应用id.
     */
    private String appId;

    /**
     * 购买的配额.
     */
    private int quota;

    /**
     * 配额类型.
     */
    private QuotaType quotaType;

    /**
     * 配额生效时间.
     */
    private Date gmtBegin;

    /**
     * 配额失效时间.
     */
    private Date gmtEnd;

    /**
     * 配额创建时间.
     */
    private Date gmtCreate;

    /**
     * 配额属性
     *
     * @see com.facishare.open.app.pay.api.cons.QuotaAttribute
     */
    private Integer attribute;

    /**
     * 订单id
     */
    private String orderId;
}
