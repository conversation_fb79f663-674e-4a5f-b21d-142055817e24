package com.facishare.open.app.pay.api.service;

import com.facishare.open.app.pay.api.model.EmployeeTrialVo;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.BaseResult;

import java.util.Date;
import java.util.List;

/**
 * 员工个人试用服务, 与企业试用隔离.
 * Created by xialf on 1/27/16.
 *
 * <AUTHOR>
 */
public interface EmployeeTrialService {
    /**
     * 增加个人试用记录.
     *
     * @param appId     应用id
     * @param fsUser    纷享用户
     * @param beginTime 试用开始时间
     * @param endTime   试用结束时间   @return 记录id
     */
    BaseResult<Long> addTrial(final String appId, FsUserVO fsUser, Date beginTime, Date endTime);

    /**
     * 查询公司中试用指定应用的员工列表.
     *
     * @param appId 应用id
     * @param fsEa  公司账号
     * @return 试用员工列表
     */
    BaseResult<List<Integer>> queryTrialUsers(final String appId, final String fsEa);

    /**
     * 查询员工可以试用的应用列表.
     *
     * @param fsUser 纷享用户
     * @return 应用列表
     */
    BaseResult<List<String>> queryTrialApps(FsUserVO fsUser);

    /**
     * 获取个人对应用的试用信息.
     *
     * @param appId  应用id
     * @param fsUser 纷享用户
     * @return 试用信息
     */
    BaseResult<EmployeeTrialVo> queryTrialInfo(final String appId, FsUserVO fsUser);

    /**
     * 获取企业中所有的个人试用.
     *
     * @param appId 应用id
     * @param fsEa  企业账号
     */
    BaseResult<List<EmployeeTrialVo>> queryTrialInfos(final String appId, final String fsEa);

    /**
     * 应用中心测试后台专用!!!
     * 删除公司对某个应用的个人试用.
     *
     * @param appId 应用id
     * @param fsEa  企业账号
     */
    BaseResult<Void> deleteTrials(final String appId, final String fsEa);
}
