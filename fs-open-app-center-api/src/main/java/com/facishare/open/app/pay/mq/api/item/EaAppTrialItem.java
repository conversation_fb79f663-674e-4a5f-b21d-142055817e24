package com.facishare.open.app.pay.mq.api.item;

import com.facishare.open.app.pay.mq.api.item.base.ProtoBase;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 企业企业试用
 *
 * Created by h<PERSON>hl on 2016/8/18.
 */
@Getter
@Setter
@ToString
public class EaAppTrialItem extends ProtoBase implements Serializable {

    private static final long serialVersionUID = 433162483167933L;

    public static final int FLAG = 2;

    /**
     * 企业账号
     */
    @Tag(1)
    private String fsEa;

    /**
     * 应用ID
     */
    @Tag(2)
    private String appId;

    /**
     * 试用配额
     */
    @Tag(3)
    private int quota;

    /**
     * 试用开始时间
     */
    @Tag(4)
    private Date beginTime;

    /**
     * 试用结束时间
     */
    @Tag(5)
    private Date endTime;

    public EaAppTrialItem() {
    }

    public EaAppTrialItem(String fsEa, String appId, int quota, Date beginTime, Date endTime) {
        this.fsEa = fsEa;
        this.appId = appId;
        this.quota = quota;
        this.beginTime = beginTime;
        this.endTime = endTime;
    }
}
