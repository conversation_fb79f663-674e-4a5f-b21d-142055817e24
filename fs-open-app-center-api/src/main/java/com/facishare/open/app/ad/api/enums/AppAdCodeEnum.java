package com.facishare.open.app.ad.api.enums;


import com.facishare.open.common.result.ErrCode;

/**
 * 错误码.
 * 应用中心－ad ,banner	79000 - 79199
 * <AUTHOR>
 * @date on 2016/01/07
 */
public enum AppAdCodeEnum implements ErrCode {

    /**
     * 成功.
     */
    SUCCESS(0, "SUCCESS", "成功"),

    /**
     * 无效的参数.
     */
    PARAM_ILLEGAL_EXCEPTION(79001, "PARAM_ILLEGAL_EXCEPTION", "无效的参数"),
    /**
     * 系统异常.
     */
    SYSTEM_EXCEPTION(79002, "SYSTEM_EXCEPTION", "系统异常");


    private final int errCode;

    private final String errMessage;

    private final String errDescription;

    AppAdCodeEnum(int errCode, String errMessage, String errDescription) {
        this.errCode = errCode;
        this.errMessage = errMessage;
        this.errDescription = errDescription;
    }


    @Override
    public int getErrCode() {
        return this.errCode;
    }

    @Override
    public String getErrMessage() {
        return this.errMessage;
    }

    @Override
    public String getErrDescription() {
        return this.errDescription;
    }
}
