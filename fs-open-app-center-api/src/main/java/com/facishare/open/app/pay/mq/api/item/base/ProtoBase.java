package com.facishare.open.app.pay.mq.api.item.base;

import io.protostuff.LinkedBuffer;
import io.protostuff.ProtobufIOUtil;
import io.protostuff.Schema;
import io.protostuff.runtime.RuntimeSchema;

/**
 * Created by huang<PERSON> on 2016/8/18.
 *
 * <AUTHOR>
 */
public class ProtoBase implements CanProto {

    @Override
    public byte[] toProto() {
        Schema schema = RuntimeSchema.getSchema(getClass());
        //noinspection unchecked
        return ProtobufIOUtil.toByteArray(this, schema, LinkedBuffer.allocate(256));
    }

    @Override
    public void fromProto(byte[] bytes) {
        Schema schema = RuntimeSchema.getSchema(getClass());
        //noinspection unchecked
        ProtobufIOUtil.mergeFrom(bytes, this, schema);
    }
}
