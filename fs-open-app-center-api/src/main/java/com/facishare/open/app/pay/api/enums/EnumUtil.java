package com.facishare.open.app.pay.api.enums;

/**
 * Created by xialf on 1/27/16.
 *
 * <AUTHOR>
 */
public class EnumUtil {
    private EnumUtil() {}

    public static <E extends Enum<E> & CodeEnum> E getByCode(Class<E> enumType, int code) {
        for (final E e: enumType.getEnumConstants()) {
            if (e.getCode() == code) {
                return e;
            }
        }
        throw new IllegalArgumentException(String.format("No enum code[%d] in [%s]",
                code, enumType.getCanonicalName()));
    }
}
