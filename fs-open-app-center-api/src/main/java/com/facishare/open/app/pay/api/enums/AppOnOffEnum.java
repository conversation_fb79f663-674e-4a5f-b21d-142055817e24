package com.facishare.open.app.pay.api.enums;


/**
 * 应用启用/停用
 * <AUTHOR>
 * @since 2016/3/21 17:39
 */
public enum AppOnOffEnum {
    /**
     * 启用.
     */
    ON("启用", 1),

    /**
     * 停用.
     */
    OFF("停用", 2),
    ;
    private String name;
    private Integer value;

    AppOnOffEnum(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public Integer getValue() {
        return value;
    }

    public static boolean isOn(Integer value) {
        return ON.value.equals(value);
    }

}
