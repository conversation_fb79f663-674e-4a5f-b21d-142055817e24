package com.facishare.open.app.pay.mq.api.item;

import com.facishare.open.app.pay.mq.api.item.base.ProtoBase;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * 企业试用到期
 *
 * Created by <PERSON><PERSON><PERSON> on 2016/8/18.
 */
@Getter
@Setter
@ToString
public class EaAppTrialExpItem extends ProtoBase implements Serializable {

    private static final long serialVersionUID = 443162483167933L;

    public static final int FLAG = 4;

    /**
     * 企业账号
     */
    @Tag(1)
    private String fsEa;

    /**
     * 应用ID
     */
    @Tag(2)
    private String appId;

    public EaAppTrialExpItem() {
    }

    public EaAppTrialExpItem(String fsEa, String appId) {
        this.fsEa = fsEa;
        this.appId = appId;
    }
}
