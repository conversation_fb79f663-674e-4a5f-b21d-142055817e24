package com.facishare.open.app.pay.mq.api.item;

import com.facishare.open.app.pay.mq.api.item.base.ProtoBase;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * 个人试用到期
 *
 * Created by <PERSON><PERSON><PERSON> on 2016/8/18.
 */
@Getter
@Setter
@ToString
public class EmployeeAppTrialExpItem extends ProtoBase implements Serializable {

    private static final long serialVersionUID = 463162483167933L;

    public static final int FLAG = 11;

    /**
     * 企业账号
     */
    @Tag(1)
    private String fsEa;

    /**
     * 用户ID
     */
    @Tag(2)
    private int userId;

    /**
     * 应用ID
     */
    @Tag(3)
    private String appId;

    public EmployeeAppTrialExpItem() {
    }

    public EmployeeAppTrialExpItem(String fsEa, int userId, String appId) {
        this.fsEa = fsEa;
        this.userId = userId;
        this.appId = appId;
    }
}
