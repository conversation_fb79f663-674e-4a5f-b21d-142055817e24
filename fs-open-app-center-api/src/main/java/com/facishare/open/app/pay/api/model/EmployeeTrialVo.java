package com.facishare.open.app.pay.api.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 个人试用记录.
 * Created by xialf on 2/25/16.
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class EmployeeTrialVo implements Serializable {
    /**
     * 企业账号.
     */
    private String fsEa;

    /**
     * 试用的用户id.
     */
    private Integer userId;

    /**
     * 应用id.
     */
    private String appId;

    /**
     * 是否可以试用.
     */
    private boolean canTryApp;

    /**
     * 试用到期时间.
     */
    private Date trialExpirationDate;

    /**
     * 试用开始时间.
     */
    private Date trialBeginDate;

    /**
     * 是否在试用中.
     *
     * @return 是否试用中
     */
    public boolean isOnTrial() {
        return trialExpirationDate != null && trialExpirationDate.after(new Date());
    }

    public boolean canTryApp() {
        return this.canTryApp;
    }
}
