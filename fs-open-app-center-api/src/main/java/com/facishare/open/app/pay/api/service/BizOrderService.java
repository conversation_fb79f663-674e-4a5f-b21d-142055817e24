package com.facishare.open.app.pay.api.service;

import com.facishare.dubbo.plugin.annotation.RestAction;
import com.facishare.open.app.pay.api.enums.OrderSource;
import com.facishare.open.app.pay.api.enums.OrderType;
import com.facishare.open.app.pay.api.model.BizOrderVo;
import com.facishare.open.common.result.BaseResult;

import java.util.List;
import java.util.Set;

/**
 * 业务订单服务，主要是一个数据存储、中转的作用.
 * 因为在架构设计上，各个应用是不可以与汇聚直接沟通的.
 * Created by xialf on 4/25/16.
 *
 * <AUTHOR>
 */
public interface BizOrderService {
    /**
     * 创建业务订单.
     *
     * @param orderSource   订单来源
     * @param sourceOrderId 源订单id(要求唯一)
     * @param orderType     订单类型(购买, 退款)
     * @param fsEa          公司账号
     * @param appId         应用id
     * @param content       订单内容
     * @param remark        备注信息(可以为空)
     * @return 记录id
     */
    BaseResult<Integer> createBizOrder(final OrderSource orderSource, final String sourceOrderId,
                                       final OrderType orderType,
                                       final String fsEa, final String appId,
                                       final String content, final String remark);

    /**
     * 查询企业-应用的业务订单列表.
     *
     * @param fsEa  公司账号
     * @param appId 应用id
     * @return 订单列表
     */
    @RestAction("queryBizOrdersAction")
    BaseResult<List<BizOrderVo>> queryBizOrders(final String fsEa, final String appId);

    /**
     * 增量查询企业-应用的业务订单列表.
     *
     * @param fsEa   公司账号
     * @param appId  应用id
     * @param lastId 上一次查询得到的最后一个id(本次不返回)
     * @return 订单列表
     */
    BaseResult<List<BizOrderVo>> increQueryBizOrders(final String fsEa, final String appId, final int lastId);

    /**
     * 从起始id并按照id递增方式查找业务订单列表.
     *
     * @param beginId      起始id(含)
     * @param limit        数目上限(与sql语句作用一致)
     * @param orderSources 订单来源
     * @return 订单列表
     */
    BaseResult<List<BizOrderVo>> queryBizOrders(int beginId, int limit, Set<OrderSource> orderSources);

    /**
     * 保存业务套餐.
     *
     * @param fsEa       公司账号
     * @param appId      应用id
     * @param pkgContent 套餐量
     */
    BaseResult<Void> saveBizPackage(final String fsEa, final String appId, final String pkgContent);

    /**
     * 查找企业-应用的业务套餐(总量, 已使用量).
     *
     * @param fsEa  公司账号
     * @param appId 应用id
     * @return 业务套餐
     */
    BaseResult<String> queryBizPackage(final String fsEa, final String appId);
}
