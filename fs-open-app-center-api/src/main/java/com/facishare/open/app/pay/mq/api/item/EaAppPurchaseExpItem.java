package com.facishare.open.app.pay.mq.api.item;

import com.facishare.open.app.pay.mq.api.item.base.ProtoBase;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * 企业购买到期
 *
 * Created by huang<PERSON> on 2016/8/18.
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class EaAppPurchaseExpItem extends ProtoBase implements Serializable {

    private static final long serialVersionUID = 423162483167933L;

    public static final int FLAG = 3;

    /**
     * 企业账号
     */
    @Tag(1)
    private String fsEa;

    /**
     * 应用ID
     */
    @Tag(2)
    private String appId;

    /**
     * 业务动作(0不发消息, 1 发消息)
     */
    @Tag(3)
    private Integer bizAction;

    public EaAppPurchaseExpItem() {
    }

    public EaAppPurchaseExpItem(String fsEa, String appId, Integer bizAction) {
        this.fsEa = fsEa;
        this.appId = appId;
        this.bizAction = bizAction;
    }
}
