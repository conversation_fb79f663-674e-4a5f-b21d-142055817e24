package com.facishare.open.app.ad.api.model;

import java.io.Serializable;
import java.util.Date;

/**
 * 精品应用的推荐数据
 *
 * <AUTHOR>
 * @date on 2016/01/07
 */
public class FsAdBestAppDO implements Serializable {
    private String id;
    private String appId;
    private Integer index;
    private Date gmtCreate;
    private Date gmtModified;

    public FsAdBestAppDO() {
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    /**
     * 获取应用id
     */
    public String getAppId() {
        return appId;
    }

    /**
     * 设置应用id
     */
    public void setAppId(String appId) {
        this.appId = appId;
    }

    /**
     * 获取排序字段
     */
    public Integer getIndex() {
        return index;
    }

    /**
     * 设置排序字段
     */
    public void setIndex(Integer index) {
        this.index = index;
    }

    /**
     * 获取创建时间
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     * 设置创建时间
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }
}
