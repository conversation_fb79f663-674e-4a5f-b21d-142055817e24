<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- ch.qos.logback.core.ConsoleAppender 控制台输出 -->
<!--    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">-->
<!--        <encoder>-->
<!--            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{12} %X{traceId} %X{userId} %msg%n</pattern>-->
<!--        </encoder>-->
<!--    </appender>-->

    <conversionRule conversionWord="msg" converterClass="com.fxiaoke.metrics.logback.MaskMessageConverter"/>

    <appender name="rest" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${catalina.home}/logs/rest.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${catalina.home}/logs/rest.%d{yyyy-MM-dd}.log.zip</fileNamePattern>
            <maxHistory>7</maxHistory>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <!-- 日志中默认打印traceId和userId，方便定位问题,异常栈中去掉包含如下字符的行避免打印很多无用的信息-->
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{12} %X{traceId} %X{userId} %msg%n
            </pattern>
        </encoder>
    </appender>

    <appender name="Error" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <file>${catalina.home}/logs/error.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${catalina.home}/logs/error.%d{yyyy-MM-dd}.log.zip</fileNamePattern>
            <maxHistory>7</maxHistory>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <!-- 日志中默认打印traceId和userId，方便定位问题,异常栈中去掉包含如下字符的行避免打印很多无用的信息-->
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{12} %X{traceId} %X{userId} %msg%n
            </pattern>
        </encoder>
    </appender>

    <appender name="OSS_Trace" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${catalina.home}/logs/trace.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${catalina.home}/logs/trace.%d{yyyy-MM-dd}.log.zip</fileNamePattern>
            <maxHistory>7</maxHistory>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <!-- 日志中默认打印traceId和userId，方便定位问题,异常栈中去掉包含如下字符的行避免打印很多无用的信息-->
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{12} %X{traceId} %X{userId} %msg%n
            </pattern>
        </encoder>
    </appender>

    <appender name="PerfLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${catalina.home}/logs/perf.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${catalina.home}/logs/perf.%d{yyyy-MM-dd}.log.zip</fileNamePattern>
            <maxHistory>7</maxHistory>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{12} %X{traceId} %X{userId} %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="REST_ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>2048</queueSize>
        <appender-ref ref="rest"/>
    </appender>

    <appender name="OSS_ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>2048</queueSize>
        <appender-ref ref="OSS_Trace"/>
    </appender>

    <appender name="PERF_ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>2048</queueSize>
        <appender-ref ref="PerfLog"/>
    </appender>

    <logger name="com.facishare.paas.expression.util.StopWatch" level="WARN" additivity="false">
        <appender-ref ref="PERF_ASYNC"/>
    </logger>

    <logger name="com.facishare.paas.appframework.common.util.StopWatch" level="WARN" additivity="false">
        <appender-ref ref="PERF_ASYNC"/>
    </logger>

    <logger name="com.facishare.paas.metadata.util.MetadataStopWatch" level="WARN" additivity="false">
        <appender-ref ref="PERF_ASYNC"/>
    </logger>

    <logger name="com.fxiaoke.common.StopWatch" level="WARN" additivity="false">
        <appender-ref ref="PERF_ASYNC"/>
    </logger>

    <logger name="com.github.trace" level="INFO" additivity="false">
        <appender-ref ref="OSS_ASYNC" />
    </logger>

    <logger name="com.github.autoconf" level="WARN" additivity="false">
        <appender-ref ref="REST_ASYNC" />
        <appender-ref ref="Error"/>
    </logger>

    <logger name="com.fxiaoke.common" level="INFO" additivity="false">
        <appender-ref ref="REST_ASYNC"/>
        <appender-ref ref="Error"/>
    </logger>

    <logger name="com.facishare.paas.metadata.cache.DescribeCache" level="WARN" additivity="false">
        <appender-ref ref="REST_ASYNC"/>
        <appender-ref ref="Error"/>
    </logger>

    <logger name="com.facishare.paas.appframework" level="INFO" additivity="false">
        <appender-ref ref="REST_ASYNC"/>
        <appender-ref ref="Error"/>
    </logger>

    <logger name="com.fxiaoke.es.service.DataService" level="ERROR" additivity="false">
        <appender-ref ref="UDOBJ_ASYNC"/>
        <appender-ref ref="Error"/>
    </logger>

    <root level="WARN">
        <appender-ref ref="REST_ASYNC"/>
    </root>
</configuration>
