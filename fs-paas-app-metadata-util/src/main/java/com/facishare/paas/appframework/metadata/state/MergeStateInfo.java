package com.facishare.paas.appframework.metadata.state;

import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.impl.ObjectData;

import java.util.Objects;
import java.util.Set;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2022/9/5
 */
public class MergeStateInfo {
    private MergeState state;
    private final IObjectData data;

    private MergeStateInfo(MergeState state, IObjectData data) {
        this.state = state;
        this.data = data;
    }

    public static MergeStateInfo of(MergeState state, IObjectData data) {
        return new MergeStateInfo(state, data);
    }

    public static MergeStateInfo empty() {
        return of(MergeState.UNKNOWN, new ObjectData());
    }

    public String uniqueKey() {
        return String.format("%s_%s", data.getDescribeApiName(), ObjectDataExt.of(data).getTemporaryId());
    }

    public boolean match(MergeStateInfo mergeStateInfo) {
        if (MergeState.UNKNOWN == state) {
            return true;
        }
        return Objects.equals(uniqueKey(), mergeStateInfo.uniqueKey());
    }

    private MergeStateInfo handleStateChange(MergeStateInfo mergeStateInfo) {
        if (!match(mergeStateInfo)) {
            throw new IllegalArgumentException();
        }
        state = state.change(mergeStateInfo.state);
        return this;
    }

    public MergeStateInfo changeAndMerge(MergeStateInfo mergeStateInfo) {
        handleStateChange(mergeStateInfo);
        if (state != MergeState.UNKNOWN) {
            Set<String> ignoreFields = getIgnoreFields(state);
            ObjectDataExt.of(data).putAllIgnoreFields(ObjectDataExt.of(mergeStateInfo.data).toMap(), ignoreFields);
        }
        return this;
    }

    public MergeStateInfo changeStateByChangeOrder(MergeStateInfo mergeStateInfo) {
        if (MergeState.UNKNOWN == state) {
            state = mergeStateInfo.state;
            return this;
        }
        return handleStateChange(mergeStateInfo);
    }

    private Set<String> getIgnoreFields(MergeState mergeState) {
        Set<String> fields = AppFrameworkConfig.getValidationFunctionMergeDetailIgnoreFieldsGray();
        // 编辑时，使用前端数据覆盖业务类型字段，即从diffMap中将record_type删除
        if (MergeState.ADD != mergeState) {
            fields.add(IFieldType.RECORD_TYPE);
        }
        return fields;
    }

    public IObjectData getData() {
        return data;
    }

    public boolean isAdd() {
        return MergeState.ADD == state;
    }

    public boolean isEdit() {
        return MergeState.EDIT == state;
    }

    public boolean isDelete() {
        return MergeState.DELETE == state;
    }

}
